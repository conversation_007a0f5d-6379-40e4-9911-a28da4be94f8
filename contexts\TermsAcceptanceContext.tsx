'use client';
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { 
  hasAcceptedTerms, 
  acceptTerms, 
  shouldShowTermsModal,
  getTermsAcceptanceData,
  TermsAcceptanceData 
} from '@/lib/termsAcceptance';

interface TermsAcceptanceContextType {
  showModal: boolean;
  acceptanceData: TermsAcceptanceData | null;
  handleAcceptTerms: () => void;
  handleDeclineTerms: () => void;
  isLoading: boolean;
}

const TermsAcceptanceContext = createContext<TermsAcceptanceContextType | undefined>(undefined);

export const useTermsAcceptance = () => {
  const context = useContext(TermsAcceptanceContext);
  if (context === undefined) {
    throw new Error('useTermsAcceptance must be used within a TermsAcceptanceProvider');
  }
  return context;
};

interface TermsAcceptanceProviderProps {
  children: ReactNode;
}

export const TermsAcceptanceProvider: React.FC<TermsAcceptanceProviderProps> = ({ children }) => {
  const [showModal, setShowModal] = useState(false);
  const [acceptanceData, setAcceptanceData] = useState<TermsAcceptanceData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check terms acceptance status on mount
  useEffect(() => {
    // Small delay to ensure proper hydration
    const timer = setTimeout(() => {
      const shouldShow = shouldShowTermsModal();
      const data = getTermsAcceptanceData();
      
      setShowModal(shouldShow);
      setAcceptanceData(data);
      setIsLoading(false);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const handleAcceptTerms = () => {
    try {
      acceptTerms();
      const updatedData = getTermsAcceptanceData();
      setAcceptanceData(updatedData);
      setShowModal(false);
    } catch (error) {
      console.error('Error accepting terms:', error);
    }
  };

  const handleDeclineTerms = () => {
    // For now, just close the modal
    // In a more strict implementation, you might redirect the user away
    // or show a message explaining that they cannot use the site without accepting
    setShowModal(false);
    
    // Show the modal again after a short delay to encourage acceptance
    setTimeout(() => {
      if (!hasAcceptedTerms()) {
        setShowModal(true);
      }
    }, 5000); // Show again after 5 seconds
  };

  const value: TermsAcceptanceContextType = {
    showModal,
    acceptanceData,
    handleAcceptTerms,
    handleDeclineTerms,
    isLoading,
  };

  return (
    <TermsAcceptanceContext.Provider value={value}>
      {children}
    </TermsAcceptanceContext.Provider>
  );
};
