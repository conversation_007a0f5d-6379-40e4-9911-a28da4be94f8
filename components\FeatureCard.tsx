import Image from "next/image";
import React from "react";
import InfoCard from "./InfoCard";

const FeatureCard: React.FC<FeatureCardProps> = ({
  image,
  ...infoCardProps
}) => {
  return (
    <div className="w-full px-5 overflow-hidden">
      <div className="overflow-hidden">
        <Image
          className="transition-transform duration-300 transform hover:scale-105"
          alt={image}
          src={image}
          width={1000}
          height={1000}
        />
      </div>
      <InfoCard {...infoCardProps} />
    </div>
  );
};

export default FeatureCard;
