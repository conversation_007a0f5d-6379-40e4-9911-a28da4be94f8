'use client'
import React, { useState, useEffect } from "react";
import { useCart } from "@/contexts/CartContext";
import { useOrders } from "@/contexts/OrderContext";
import { useAuth } from "@/contexts/AuthContext";
import { getCurrentUser } from "@/apis/user";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { useCurrency } from "@/contexts/CurrencyContext";
import Link from "next/link";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import WhatsAppOrderButton from "@/components/WhatsAppOrderButton";
import { WhatsAppOrderData } from "@/utils/whatsappOrder";

const CheckoutPage = () => {
  const { cart, clearCart } = useCart();
  const { createOrder } = useOrders();
  const { isAuthenticated, user } = useAuth();
  const { formatPrice, selectedCurrency } = useCurrency();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingUserData, setIsLoadingUserData] = useState(false);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    postalCode: "",
  });

  // Fetch user data and pre-fill form when component mounts
  useEffect(() => {
    const fetchUserData = async () => {
      if (isAuthenticated) {
        setIsLoadingUserData(true);
        try {
          // Get detailed user data from API
          const userData = await getCurrentUser();

          if (userData) {
            // Split name into first and last name
            const nameParts = userData.name.split(" ");
            const firstName = nameParts[0] || "";
            const lastName = nameParts.slice(1).join(" ") || "";

            // Update form with user data
            setFormData((prevData) => ({
              ...prevData,
              firstName,
              lastName,
              email: userData.email || "",
              phone: userData.phone || "",
            }));

            console.log("User data loaded successfully for checkout form");
          }
        } catch (error) {
          console.error("Error fetching user data for checkout form:", error);
          toast.error(
            "Failed to load your profile data. You can still fill the form manually."
          );
        } finally {
          setIsLoadingUserData(false);
        }
      }
    };

    fetchUserData();
  }, [isAuthenticated]);

  // Redirect to cart if cart is empty
  if (cart.items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-2xl font-bold mb-4">Your cart is empty</h1>
        <p className="mb-8">
          You need to add items to your cart before checking out.
        </p>
        <Link href="/products">
          <Button variant="default">Browse Products</Button>
        </Link>
      </div>
    );
  }

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleWhatsAppOrder = async (): Promise<WhatsAppOrderData> => {
    // Validate form
    const requiredFields = [
      "firstName",
      "lastName",
      "email",
      "phone",
      "address",
      "city",
    ];
    const missingFields = requiredFields.filter(
      (field) => !formData[field as keyof typeof formData]
    );

    if (missingFields.length > 0) {
      toast.error("Please fill in all required fields");
      throw new Error("Missing required fields");
    }

    if (!isAuthenticated || !user?.id) {
      toast.error("You must be logged in to place an order");
      throw new Error("User not authenticated");
    }

    setIsLoading(true);

    try {
      // Create order data
      const orderData = {
        items: cart.items,
        total: cart.total,
        subtotal: cart.subtotal,
        discount: cart.discount,
        customer: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
        },
        shipping: {
          address: formData.address,
          city: formData.city,
          postalCode: formData.postalCode,
        },
        paymentMethod: "cash" as "cash" | "bank", // Default to cash for WhatsApp orders
        status: "pending" as const,
      };

      // Create order in database
      const createdOrder = await createOrder(orderData);

      // Prepare WhatsApp order data with the created order
      const whatsAppOrderData = {
        order: createdOrder,
        customerDetails: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          address: formData.address,
          city: formData.city,
          postalCode: formData.postalCode,
        },
        formatPrice, // Pass the currency formatter from context
        selectedCurrency, // Pass the selected currency
      };

      // Clear cart after successful order creation
      clearCart();
      
      toast.success("Order created successfully! Redirecting to WhatsApp...");
      
      // Redirect to orders page after a short delay
      setTimeout(() => {
        router.push("/orders");
      }, 2000);
      
      // Return the WhatsApp order data for the button to handle
      return whatsAppOrderData;
    } catch (error) {
      console.error("Error creating order:", error);
      toast.error("Failed to create order. Please try again.");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-6 sm:py-8">
        <h1 className="text-xl sm:text-2xl font-bold mb-4 sm:mb-8">Order via WhatsApp</h1>

        {/* Mobile Order Summary Toggle - Only visible on small screens */}
        <div className="block lg:hidden mb-6">
          <details className="bg-white rounded-lg shadow-sm overflow-hidden">
            <summary className="p-4 font-medium flex justify-between items-center cursor-pointer">
              <span>Order Summary ({cart.items.length} items)</span>
              <span className="font-semibold">
                {formatPrice(cart.total)}
              </span>
            </summary>
            <div className="p-4 pt-0 border-t border-gray-100">
              <div className="max-h-[200px] overflow-y-auto mb-4">
                {cart.items.map((item) => (
                  <div
                    key={item.product.id}
                    className="flex items-center gap-3 mb-3 pb-3 border-b border-gray-100"
                  >
                    <div className="w-12 h-12 bg-gray-50 rounded-md overflow-hidden flex-shrink-0">
                      <img
                        src={item.product.image}
                        alt={item.product.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-grow">
                      <h3 className="text-sm font-medium truncate">
                        {item.product.title}
                      </h3>
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-xs text-gray-500">
                          Qty: {item.quantity}
                        </span>
                        <span className="text-xs font-medium">
                          {formatPrice(item.product.price * item.quantity)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subtotal</span>
                  <span>{formatPrice(cart.subtotal)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Discount</span>
                  <span className="text-accent">
                    -{formatPrice(cart.discount)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Shipping</span>
                  <span>Free</span>
                </div>
              </div>
            </div>
          </details>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Checkout Form */}
          <div className="lg:col-span-2">
            <motion.div
              className="bg-white rounded-lg shadow-sm p-4 sm:p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">Shipping Information</h2>
                {isAuthenticated &&
                  !isLoadingUserData &&
                  (formData.firstName || formData.email) && (
                    <div className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">
                      ✓ Information pre-filled from your profile
                    </div>
                  )}
              </div>

              {isLoadingUserData ? (
                <div className="mb-6">
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-accent"></div>
                    <span className="ml-2 text-sm text-gray-600">
                      Loading your information...
                    </span>
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <label
                      htmlFor="firstName"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      First Name *
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50"
                      required
                      disabled={isLoadingUserData}
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="lastName"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Last Name *
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50"
                      required
                      disabled={isLoadingUserData}
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50"
                      required
                      disabled={isLoadingUserData}
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="phone"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50"
                      required
                      disabled={isLoadingUserData}
                      pattern="[0-9]{11}"
                      title="Phone number must be 11 digits"
                    />
                  </div>
                </div>
              )}

              <div className="mb-6">
                <label
                  htmlFor="address"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Address *
                </label>
                <textarea
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <label
                    htmlFor="city"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    City *
                  </label>
                  <input
                    type="text"
                    id="city"
                    name="city"
                    value={formData.city}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50"
                    required
                  />
                </div>

                <div>
                  <label
                    htmlFor="postalCode"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Postal Code
                  </label>
                  <input
                    type="text"
                    id="postalCode"
                    name="postalCode"
                    value={formData.postalCode}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50"
                  />
                </div>
              </div>

              <h2 className="text-lg font-semibold mb-4">Order Information</h2>
              <p className="text-sm text-gray-600 mb-6">
                Fill out your information below to place an order via WhatsApp. Your order will be created and saved, then you'll be directed to WhatsApp to confirm the details with us.
              </p>

              <div className="flex justify-between items-center">
                <Link href="/cart">
                  <Button variant="ghost" type="button">
                    Back to Cart
                  </Button>
                </Link>

                <WhatsAppOrderButton
                  onOrderCreate={handleWhatsAppOrder}
                  disabled={isLoading || !formData.firstName || !formData.lastName || !formData.email || !formData.phone || !formData.address || !formData.city}
                  className="min-w-[200px]"
                >
                  {isLoading ? "Processing..." : "Order via WhatsApp"}
                </WhatsAppOrderButton>
              </div>
            </motion.div>
          </div>

          {/* Order Summary - Desktop Only */}
          <div className="hidden lg:block lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-4 sticky top-24">
              <h2 className="text-lg font-semibold mb-4">Order Summary</h2>

              <div className="max-h-[300px] overflow-y-auto mb-4">
                {cart.items.map((item) => (
                  <div
                    key={item.product.id}
                    className="flex items-center gap-3 mb-3 pb-3 border-b border-gray-100"
                  >
                    <div className="w-16 h-16 bg-gray-50 rounded-md overflow-hidden flex-shrink-0">
                      <img
                        src={item.product.image}
                        alt={item.product.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-grow">
                      <h3 className="text-sm font-medium line-clamp-1">
                        {item.product.title}
                      </h3>
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-sm text-gray-500">
                          Qty: {item.quantity}
                        </span>
                        <span className="text-sm font-medium">
                          {formatPrice(item.product.price * item.quantity)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span>{formatPrice(cart.subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Discount</span>
                  <span className="text-accent">
                    -{formatPrice(cart.discount)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span>Free</span>
                </div>
                <div className="border-t border-gray-100 pt-2 mt-2">
                  <div className="flex justify-between font-semibold">
                    <span>Total</span>
                    <span>{formatPrice(cart.total)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
};

export default CheckoutPage;
