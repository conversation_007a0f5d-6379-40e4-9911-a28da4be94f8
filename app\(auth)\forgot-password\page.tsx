'use client';
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { ArrowLeft } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { forgotPassword, isLoading } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!email) {
      toast.error('Please enter your email address');
      return;
    }

    try {
      // Call the forgotPassword method from AuthContext
      await forgotPassword(email);

      // Success - show confirmation and then redirect to OTP verification
      setIsSubmitted(true);

      // Wait 2 seconds before redirecting to OTP verification page
      setTimeout(() => {
        window.location.href = `/verify-otp?email=${encodeURIComponent(email)}`;
      }, 2000);
    } catch (error) {
      // Error handling is done in the AuthContext
    }
  };

  const handleResendEmail = async () => {
    try {
      // Call the forgotPassword method again to resend the OTP
      await forgotPassword(email);
    } catch (error) {
      // Error handling is done in the AuthContext
    }
  };

  return (
    <div className="bg-card rounded-lg shadow-lg p-8 w-full">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <Link
          href="/sign-in"
          className="inline-flex items-center text-sm text-accent hover:underline mb-6"
        >
          <ArrowLeft className="mr-1 h-4 w-4" />
          {/* Back to Sign In */}
        </Link>

        {!isSubmitted ? (
          <>
            <h2 className="text-2xl font-bold text-center mb-2">
              Forgot Password
            </h2>
            <p className="text-center text-muted-foreground mb-6 text-sm">
              Enter your email address and we'll send you a link to reset your
              password.
            </p>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-medium">
                  Email Address
                </label>
                <motion.div whileFocus={{ scale: 1.01 }} className="relative">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all"
                    placeholder="<EMAIL>"
                  />
                </motion.div>
              </div>

              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="pt-2"
              >
                <Button
                  type="submit"
                  variant="default"
                  className="w-full py-2"
                  disabled={isLoading}
                >
                  {isLoading ? 'Sending...' : 'Reset Password'}
                </Button>
              </motion.div>
            </form>
          </>
        ) : (
          <div className="text-center">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="mx-auto w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-8 w-8 text-accent"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-bold mb-2">Check Your Email</h2>
              <p className="text-muted-foreground mb-6 text-sm">
                We've sent a 6-digit OTP code to{' '}
                <span className="font-medium text-foreground">{email}</span>
              </p>
              <p className="text-sm text-muted-foreground mb-6">
                You'll be redirected to enter the OTP code. Didn't receive the
                email? Check your spam folder or
              </p>
              <Button
                variant="outline"
                onClick={handleResendEmail}
                disabled={isLoading}
                className="mb-4"
              >
                {isLoading ? 'Sending...' : 'Resend OTP'}
              </Button>
            </motion.div>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default ForgotPassword;
