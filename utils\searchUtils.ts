// Import ProductData from global types
/// <reference path="../types/index.d.ts" />

/**
 * Interface for search term suggestions
 */
export interface SearchTermSuggestion {
  term: string;
  count: number;
  category?: string;
}

/**
 * Search products based on a query string
 * @param products Array of products to search through
 * @param query Search query string
 * @param limit Optional limit for number of results
 * @returns Filtered array of products matching the query
 */
export const searchProducts = (
  products: ProductData[],
  query: string,
  limit?: number
): ProductData[] => {
  if (!query || query.trim() === "") {
    return limit ? products.slice(0, limit) : products;
  }

  const normalizedQuery = query.toLowerCase().trim();
  const queryTerms = normalizedQuery.split(/\s+/).filter(Boolean);

  // If no valid query terms, return all products (or limited subset)
  if (queryTerms.length === 0) {
    return limit ? products.slice(0, limit) : products;
  }

  // Score-based search
  const scoredProducts = products.map((product) => {
    let score = 0;
    const title = product.title.toLowerCase();
    const category = product.category.toLowerCase();
    const description = product.description?.toLowerCase() || "";

    // Check each query term
    for (const term of queryTerms) {
      // Exact matches in title get highest score
      if (title === term) score += 100;
      // Title starts with term
      else if (title.startsWith(term)) score += 80;
      // Title contains term
      else if (title.includes(term)) score += 60;

      // Category matches
      if (category === term) score += 50;
      else if (category.includes(term)) score += 30;

      // Description contains term
      if (description.includes(term)) score += 20;
    }

    return { product, score };
  });

  // Filter out products with zero score and sort by score (descending)
  const filteredProducts = scoredProducts
    .filter((item) => item.score > 0)
    .sort((a, b) => b.score - a.score)
    .map((item) => item.product);

  // Apply limit if specified
  return limit ? filteredProducts.slice(0, limit) : filteredProducts;
};

/**
 * Extract search terms from products
 * @param products Array of products to extract terms from
 * @returns Array of unique search terms with counts
 */
export const extractSearchTerms = (
  products: ProductData[]
): SearchTermSuggestion[] => {
  const termMap = new Map<string, SearchTermSuggestion>();

  // Process each product
  products.forEach((product) => {
    // Add title words as terms
    const titleWords = product.title
      .toLowerCase()
      .split(/\s+/)
      .filter((word: string) => word.length > 2);
    titleWords.forEach((word: string) => {
      if (termMap.has(word)) {
        termMap.get(word)!.count++;
      } else {
        termMap.set(word, { term: word, count: 1 });
      }
    });

    // Add category as a term
    const category = product.category.toLowerCase();
    if (termMap.has(category)) {
      termMap.get(category)!.count++;
    } else {
      termMap.set(category, {
        term: category,
        count: 1,
        category: product.category,
      });
    }

    // Add common phrases from title (2-3 word combinations)
    if (titleWords.length > 1) {
      for (let i = 0; i < titleWords.length - 1; i++) {
        const phrase = `${titleWords[i]} ${titleWords[i + 1]}`;
        if (termMap.has(phrase)) {
          termMap.get(phrase)!.count++;
        } else {
          termMap.set(phrase, { term: phrase, count: 1 });
        }
      }
    }
  });

  // Convert map to array and sort by count (descending)
  return Array.from(termMap.values()).sort((a, b) => b.count - a.count);
};

/**
 * Get search term suggestions based on a query string
 * @param products Array of products to search through
 * @param query Search query string
 * @param maxSuggestions Maximum number of suggestions to return
 * @returns Array of search term suggestions
 */
export const getSearchTermSuggestions = (
  products: ProductData[],
  query: string,
  maxSuggestions: number = 5
): SearchTermSuggestion[] => {
  if (!query || query.trim() === "") {
    return [];
  }

  const normalizedQuery = query.toLowerCase().trim();
  const allTerms = extractSearchTerms(products);

  // Filter terms that match the query
  const matchingTerms = allTerms.filter(
    (term) =>
      term.term.includes(normalizedQuery) ||
      (term.category && term.category.toLowerCase().includes(normalizedQuery))
  );

  // Return limited number of suggestions
  return matchingTerms.slice(0, maxSuggestions);
};

/**
 * Get product search suggestions based on a query string
 * @param products Array of products to search through
 * @param query Search query string
 * @param maxSuggestions Maximum number of suggestions to return
 * @returns Array of product suggestions
 */
export const getProductSuggestions = (
  products: ProductData[],
  query: string,
  maxSuggestions: number = 5
): ProductData[] => {
  return searchProducts(products, query, maxSuggestions);
};

/**
 * Get combined search suggestions (terms and products)
 * @param products Array of products to search through
 * @param query Search query string
 * @param maxTermSuggestions Maximum number of term suggestions
 * @param maxProductSuggestions Maximum number of product suggestions
 * @returns Object containing term and product suggestions
 */
export const getSearchSuggestions = (
  products: ProductData[],
  query: string,
  maxTermSuggestions: number = 3,
  maxProductSuggestions: number = 3
): { terms: SearchTermSuggestion[]; products: ProductData[] } => {
  return {
    terms: getSearchTermSuggestions(products, query, maxTermSuggestions),
    products: getProductSuggestions(products, query, maxProductSuggestions),
  };
};
