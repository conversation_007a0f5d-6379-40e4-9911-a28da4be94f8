"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { motion } from "framer-motion";
import { siteConfig } from "@/config/site";

export default function TermsOfServicePage() {
  const lastUpdated = "January 2024";

  return (
    <div className="container mx-auto py-16 px-4 md:px-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-12"
      >
        <h1 className="text-4xl font-bold mb-4">Terms of Service</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Please read these terms carefully before using our services. By using our website, you agree to these terms and conditions.
        </p>
        <p className="text-sm text-muted-foreground mt-4">
          Last updated: {lastUpdated}
        </p>
      </motion.div>

      <div className="max-w-4xl mx-auto space-y-8">
        {/* Acceptance of Terms */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Acceptance of Terms</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p>
                Welcome to {siteConfig.name}. These Terms of Service ("Terms") govern your use of our website 
                {siteConfig.seo.domain} and the purchase of products from us. By accessing or using our services, 
                you agree to be bound by these Terms.
              </p>
              <p>
                If you do not agree to these Terms, please do not use our website or services. 
                We reserve the right to modify these Terms at any time, and your continued use 
                constitutes acceptance of any changes.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Use of Website */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Use of Website</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <h4>Permitted Use</h4>
              <p>You may use our website for lawful purposes only. You agree to:</p>
              <ul>
                <li>Provide accurate and complete information when creating an account</li>
                <li>Maintain the security of your account credentials</li>
                <li>Use the website in compliance with all applicable laws</li>
                <li>Respect the intellectual property rights of others</li>
              </ul>

              <h4>Prohibited Activities</h4>
              <p>You agree not to:</p>
              <ul>
                <li>Use the website for any illegal or unauthorized purpose</li>
                <li>Attempt to gain unauthorized access to our systems</li>
                <li>Interfere with the proper functioning of the website</li>
                <li>Upload or transmit harmful content or malware</li>
                <li>Violate any applicable laws or regulations</li>
                <li>Impersonate another person or entity</li>
              </ul>
            </CardContent>
          </Card>
        </motion.div>

        {/* Products and Services */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Products and Services</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <h4>Product Information</h4>
              <p>
                We strive to provide accurate product descriptions, images, and pricing. However, 
                we do not warrant that product descriptions or other content is accurate, complete, 
                reliable, or error-free. Colors and details may vary from images shown.
              </p>

              <h4>Custom Orders</h4>
              <p>
                For custom furniture orders, specific terms apply:
              </p>
              <ul>
                <li>Custom orders require advance payment and detailed specifications</li>
                <li>Production time varies based on complexity and current workload</li>
                <li>Custom orders are non-refundable once production begins</li>
                <li>Final approval of design and specifications is required before production</li>
                <li>Delivery timelines are estimates and may vary due to craftsmanship requirements</li>
              </ul>

              <h4>Availability</h4>
              <p>
                All products are subject to availability. We reserve the right to discontinue 
                products or modify specifications without notice. If a product becomes unavailable 
                after your order, we will notify you and offer alternatives or a full refund.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Pricing and Payment */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Pricing and Payment</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <h4>Pricing</h4>
              <p>
                All prices are displayed in USD and PKR (Pakistani Rupees) with a fixed exchange rate of $1 = 282 PKR. 
                Prices include applicable taxes unless otherwise stated. We reserve the right to change prices 
                without notice, but price changes will not affect orders already placed.
              </p>

              <h4>Payment Methods</h4>
              <p>We accept the following payment methods:</p>
              <ul>
                <li>Credit and debit cards (Visa, MasterCard)</li>
                <li>Bank transfers (for local customers)</li>
                <li>Digital payment platforms</li>
              </ul>

              <h4>Payment Terms</h4>
              <ul>
                <li>Payment is required at the time of order placement</li>
                <li>For custom orders, a 50% deposit is required to begin production</li>
                <li>All payments are processed securely through encrypted channels</li>
                <li>Failed payments may result in order cancellation</li>
              </ul>
            </CardContent>
          </Card>
        </motion.div>

        {/* Shipping and Delivery */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Shipping and Delivery</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <h4>Shipping Areas</h4>
              <p>
                We primarily serve customers in Pakistan, with special arrangements for international shipping. 
                Shipping costs and delivery times vary based on location and product size.
              </p>

              <h4>Delivery Terms</h4>
              <ul>
                <li>Delivery times are estimates and may vary due to factors beyond our control</li>
                <li>Large furniture items may require special delivery arrangements</li>
                <li>Customer must be available to receive delivery at the specified address</li>
                <li>Delivery includes placement in the ground floor or agreed location</li>
                <li>Assembly services may be available for an additional fee</li>
              </ul>

              <h4>Risk of Loss</h4>
              <p>
                Risk of loss and title for products pass to you upon delivery. We recommend 
                inspecting items upon delivery and reporting any damage immediately.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Returns and Refunds */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Returns and Refunds</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <h4>Return Policy</h4>
              <p>
                Due to the nature of our handcrafted furniture, returns are limited to specific circumstances:
              </p>
              <ul>
                <li>Manufacturing defects reported within 7 days of delivery</li>
                <li>Damage during shipping (must be reported within 48 hours)</li>
                <li>Significant deviation from agreed specifications</li>
              </ul>

              <h4>Non-Returnable Items</h4>
              <ul>
                <li>Custom-made furniture designed to your specifications</li>
                <li>Items damaged due to misuse or normal wear</li>
                <li>Products modified or altered after delivery</li>
              </ul>

              <h4>Refund Process</h4>
              <p>
                Approved refunds will be processed within 7-14 business days to the original payment method. 
                Shipping costs are non-refundable unless the return is due to our error.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Warranties and Disclaimers */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Warranties and Disclaimers</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <h4>Product Warranty</h4>
              <p>
                We warrant our furniture against manufacturing defects for a period of 1 year from delivery.
                This warranty covers:
              </p>
              <ul>
                <li>Structural integrity of joints and construction</li>
                <li>Quality of wood and finishing materials</li>
                <li>Hardware functionality</li>
              </ul>

              <h4>Warranty Exclusions</h4>
              <p>The warranty does not cover:</p>
              <ul>
                <li>Normal wear and tear</li>
                <li>Damage from misuse, abuse, or accidents</li>
                <li>Exposure to extreme conditions</li>
                <li>Modifications made by third parties</li>
                <li>Natural variations in wood grain and color</li>
              </ul>

              <h4>Disclaimer</h4>
              <p>
                Except as expressly stated, all products are provided "as is" without warranties of any kind.
                We disclaim all other warranties, express or implied, including merchantability and fitness
                for a particular purpose.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Limitation of Liability */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Limitation of Liability</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p>
                To the maximum extent permitted by law, {siteConfig.name} shall not be liable for any indirect,
                incidental, special, consequential, or punitive damages, including but not limited to:
              </p>
              <ul>
                <li>Loss of profits or revenue</li>
                <li>Loss of data or business interruption</li>
                <li>Personal injury or property damage</li>
                <li>Delays in delivery or service</li>
              </ul>
              <p>
                Our total liability for any claim shall not exceed the amount paid for the specific product or service.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Intellectual Property */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.9 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Intellectual Property</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p>
                All content on this website, including text, graphics, logos, images, and software,
                is the property of {siteConfig.name} or its licensors and is protected by copyright,
                trademark, and other intellectual property laws.
              </p>
              <p>
                You may not reproduce, distribute, modify, or create derivative works from our content
                without express written permission. Our furniture designs and craftsmanship techniques
                represent our proprietary methods and trade secrets.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Privacy */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 1.0 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Privacy</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p>
                Your privacy is important to us. Please review our Privacy Policy, which also governs
                your use of our services, to understand our practices regarding the collection and use
                of your personal information.
              </p>
              <p>
                By using our services, you consent to the collection and use of information as described
                in our Privacy Policy.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Governing Law */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 1.1 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Governing Law and Dispute Resolution</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p>
                These Terms shall be governed by and construed in accordance with the laws of Pakistan.
                Any disputes arising from these Terms or your use of our services shall be resolved through:
              </p>
              <ol>
                <li>Good faith negotiation between the parties</li>
                <li>Mediation, if negotiation fails</li>
                <li>Arbitration or court proceedings in Chiniot, Punjab, Pakistan</li>
              </ol>
              <p>
                You agree to submit to the personal jurisdiction of the courts located in Punjab, Pakistan
                for any legal proceedings.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Changes to Terms */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 1.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Changes to Terms</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p>
                We reserve the right to modify these Terms at any time. Changes will be effective immediately
                upon posting on our website. We will notify users of material changes through:
              </p>
              <ul>
                <li>Email notifications to registered users</li>
                <li>Prominent notices on our website</li>
                <li>Updates to the "Last updated" date</li>
              </ul>
              <p>
                Your continued use of our services after changes are posted constitutes acceptance of the modified Terms.
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Contact Information */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 1.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p>
                If you have any questions about these Terms of Service, please contact us:
              </p>
              <div className="not-prose">
                <div className="space-y-2">
                  <p><strong>Email:</strong> {siteConfig.contact.email}</p>
                  <p><strong>Phone:</strong> {siteConfig.contact.phone}</p>
                  <p><strong>WhatsApp:</strong> {siteConfig.contact.whatsapp}</p>
                  <p><strong>Address:</strong></p>
                  <div className="ml-4">
                    <p>{siteConfig.contact.address.street}</p>
                    <p>{siteConfig.contact.address.city}, {siteConfig.contact.address.region} {siteConfig.contact.address.postalCode}</p>
                    <p>{siteConfig.contact.address.country}</p>
                  </div>
                </div>
              </div>
              <p className="mt-4">
                We are committed to resolving any concerns you may have about our terms and services.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
