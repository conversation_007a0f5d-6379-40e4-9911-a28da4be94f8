"use client";

import Script from "next/script";
import { siteConfig, getUrl } from "@/config/site";

interface LocalSEOSchemaProps {
  businessType?: string;
  additionalServices?: string[];
  openingHours?: {
    [key: string]: { open: string; close: string };
  };
  specialOffers?: string[];
  serviceArea?: string[];
  nearbyLandmarks?: string[];
}

/**
 * Local SEO Schema component for Chiniot furniture business
 * Optimized for local search visibility in Pakistan
 */
export default function LocalSEOSchema({
  businessType = "FurnitureStore",
  additionalServices = [],
  openingHours,
  specialOffers = [],
  serviceArea = [],
  nearbyLandmarks = [],
}: LocalSEOSchemaProps) {
  
  // Enhanced Local Business Schema with Chiniot context
  const localBusinessSchema = {
    "@context": "https://schema.org",
    "@type": [businessType, "LocalBusiness", "Store"],
    "@id": `${getUrl()}/#localbusiness`,
    name: siteConfig.name,
    alternateName: [
      siteConfig.shortName,
      "Chiniot Furniture Store",
      "Chinioti Wooden Art Furniture",
      "Authentic Chiniot Furniture"
    ],
    description: `${siteConfig.description} Located in Chiniot, Punjab, Pakistan - the world's furniture manufacturing capital. Specializing in authentic handcrafted wooden furniture using traditional techniques passed down through 500+ years.`,
    
    // Business identity and branding
    url: getUrl(),
    logo: getUrl("/logo.svg"),
    image: [
      getUrl("/og-image.jpg"),
      getUrl("/assets/backgrounds/home-bg.png"),
      getUrl("/logo.svg")
    ],
    
    // Contact information
    telephone: siteConfig.contact.phone,
    email: siteConfig.contact.email,
    
    // Enhanced address with local context
    address: {
      "@type": "PostalAddress",
      streetAddress: siteConfig.contact.address.street,
      addressLocality: siteConfig.contact.address.city,
      addressRegion: siteConfig.contact.address.region,
      postalCode: siteConfig.contact.address.postalCode,
      addressCountry: siteConfig.contact.address.country,
      // Additional local identifiers
      addressCountryCode: "PK",
      addressRegionCode: "PB"
    },
    
    // Geographic coordinates
    geo: {
      "@type": "GeoCoordinates",
      latitude: siteConfig.seo.location.latitude,
      longitude: siteConfig.seo.location.longitude,
      address: {
        "@type": "PostalAddress",
        addressLocality: "Chiniot",
        addressRegion: "Punjab",
        addressCountry: "Pakistan"
      }
    },
    
    // Service area - enhanced for international reach
    areaServed: [
      ...siteConfig.seo.organization.areaServed,
      ...serviceArea,
      {
        "@type": "Country",
        name: "Pakistan",
        sameAs: "https://en.wikipedia.org/wiki/Pakistan"
      },
      {
        "@type": "State",
        name: "Punjab",
        containedInPlace: {
          "@type": "Country",
          name: "Pakistan"
        }
      },
      {
        "@type": "City",
        name: "Chiniot",
        containedInPlace: {
          "@type": "State",
          name: "Punjab"
        },
        sameAs: "https://en.wikipedia.org/wiki/Chiniot"
      }
    ],
    
    // Enhanced opening hours
    openingHoursSpecification: [
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
        opens: openingHours?.weekdays?.open || siteConfig.seo.businessHours.weekdays.open,
        closes: openingHours?.weekdays?.close || siteConfig.seo.businessHours.weekdays.close,
        validFrom: new Date().toISOString().split('T')[0],
        validThrough: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      },
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: "Saturday",
        opens: openingHours?.saturday?.open || siteConfig.seo.businessHours.saturday.open,
        closes: openingHours?.saturday?.close || siteConfig.seo.businessHours.saturday.close
      },
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: "Sunday",
        opens: openingHours?.sunday?.open || siteConfig.seo.businessHours.sunday.open,
        closes: openingHours?.sunday?.close || siteConfig.seo.businessHours.sunday.close
      }
    ],
    
    // Business details
    foundingDate: siteConfig.seo.organization.foundingDate,
    priceRange: siteConfig.seo.organization.priceRange,
    paymentAccepted: siteConfig.seo.organization.paymentAccepted,
    currenciesAccepted: siteConfig.seo.organization.currenciesAccepted,
    
    // Enhanced services and specialties
    hasOfferCatalog: {
      "@type": "OfferCatalog",
      name: "Chiniot Furniture Collection",
      itemListElement: [
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Product",
            name: "Handcrafted Wooden Beds",
            category: "Bedroom Furniture"
          }
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Product",
            name: "Traditional Dining Sets",
            category: "Dining Room Furniture"
          }
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Product",
            name: "Custom Wooden Wardrobes",
            category: "Storage Furniture"
          }
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Product",
            name: "Carved Wooden Chairs",
            category: "Seating Furniture"
          }
        }
      ]
    },
    
    // Services offered
    makesOffer: [
      {
        "@type": "Offer",
        name: "Custom Furniture Design",
        description: "Bespoke furniture design services using traditional Chiniot techniques"
      },
      {
        "@type": "Offer",
        name: "International Shipping",
        description: "Worldwide delivery of authentic Chiniot furniture"
      },
      {
        "@type": "Offer",
        name: "Furniture Restoration",
        description: "Professional restoration of wooden furniture"
      },
      ...additionalServices.map(service => ({
        "@type": "Offer",
        name: service,
        description: `Professional ${service.toLowerCase()} services`
      }))
    ],
    
    // Aggregate rating
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: siteConfig.seo.richSnippets.aggregateRating.ratingValue,
      reviewCount: siteConfig.seo.richSnippets.aggregateRating.reviewCount,
      bestRating: siteConfig.seo.richSnippets.aggregateRating.bestRating,
      worstRating: siteConfig.seo.richSnippets.aggregateRating.worstRating,
      ratingExplanation: "Customer ratings for authentic Chiniot furniture quality and craftsmanship"
    },
    
    // Social media presence
    sameAs: [
      siteConfig.social.facebook,
      siteConfig.social.instagram,
      siteConfig.social.twitter,
      siteConfig.social.youtube,
      "https://en.wikipedia.org/wiki/Chiniot",
      "https://www.google.com/maps/place/Chiniot,+Punjab,+Pakistan"
    ],
    
    // Keywords for local SEO
    keywords: [
      "Chiniot furniture",
      "furniture Chiniot Punjab",
      "Pakistani furniture",
      "handcrafted wooden furniture Pakistan",
      "traditional furniture makers",
      "Chiniot artisans",
      "wooden furniture manufacturer Pakistan",
      "authentic Chiniot craftsmanship",
      "furniture export Pakistan",
      "Chiniot furniture market"
    ].join(", "),
    
    // Additional properties for local context
    additionalProperty: [
      {
        "@type": "PropertyValue",
        name: "Specialization",
        value: "Traditional Chiniot Furniture Craftsmanship"
      },
      {
        "@type": "PropertyValue",
        name: "Heritage",
        value: "500+ Years of Woodworking Tradition"
      },
      {
        "@type": "PropertyValue",
        name: "Location Significance",
        value: "Located in Chiniot - World's Furniture Manufacturing Capital"
      },
      {
        "@type": "PropertyValue",
        name: "Craftsmanship Style",
        value: "Authentic Pakistani Traditional Techniques"
      },
      {
        "@type": "PropertyValue",
        name: "Materials",
        value: "Premium Pakistani Hardwoods (Sheesham, Teak, Mango)"
      }
    ],
    
    // Nearby landmarks and points of interest
    ...(nearbyLandmarks.length > 0 && {
      nearbyLandmarks: nearbyLandmarks.map(landmark => ({
        "@type": "Place",
        name: landmark,
        address: {
          "@type": "PostalAddress",
          addressLocality: "Chiniot",
          addressRegion: "Punjab",
          addressCountry: "Pakistan"
        }
      }))
    }),
    
    // Special offers and promotions
    ...(specialOffers.length > 0 && {
      hasOfferCatalog: {
        "@type": "OfferCatalog",
        name: "Special Offers",
        itemListElement: specialOffers.map(offer => ({
          "@type": "Offer",
          name: offer,
          validFrom: new Date().toISOString(),
          validThrough: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        }))
      }
    })
  };

  return (
    <Script
      id="local-business-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(localBusinessSchema)
      }}
    />
  );
}

// Helper function to generate location-specific keywords
export function generateLocalKeywords(location: string, businessType: string) {
  const baseKeywords = [
    `${businessType} ${location}`,
    `${businessType} near ${location}`,
    `best ${businessType} ${location}`,
    `${businessType} in ${location}`,
    `${location} ${businessType}`,
    `${businessType} ${location} Pakistan`,
    `handcrafted ${businessType} ${location}`,
    `traditional ${businessType} ${location}`,
    `authentic ${businessType} ${location}`,
    `custom ${businessType} ${location}`
  ];
  
  return baseKeywords;
}

// Local business hours formatter
export function formatBusinessHours(hours: any) {
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  return days.map(day => {
    const dayKey = day.toLowerCase();
    const dayHours = hours[dayKey] || hours.weekdays;
    return `${day}: ${dayHours.open} - ${dayHours.close}`;
  }).join(', ');
}
