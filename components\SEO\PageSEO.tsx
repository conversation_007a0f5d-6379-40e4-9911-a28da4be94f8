import { Metadata } from "next";
import { siteConfig, getUrl } from "@/config/site";

interface PageSEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: "website" | "article" | "product" | "profile";
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
  noIndex?: boolean;
  canonical?: string;
}

/**
 * Generate comprehensive SEO metadata for pages
 * Optimized for Chiniot furniture business and local SEO
 */
export function generatePageSEO({
  title,
  description,
  keywords = [],
  image,
  url,
  type = "website",
  publishedTime,
  modifiedTime,
  author,
  section,
  tags = [],
  noIndex = false,
  canonical,
}: PageSEOProps): Metadata {
  // Construct page title with Chiniot furniture context
  const pageTitle = title 
    ? `${title} | ${siteConfig.name} - Premium Chiniot Furniture`
    : `${siteConfig.name} - Premium Handcrafted Furniture from Chiniot, Pakistan`;

  // Enhanced description with local SEO
  const pageDescription = description 
    ? `${description} Discover authentic Chiniot furniture crafted by skilled artisans in Punjab, Pakistan.`
    : `${siteConfig.description} Explore our collection of premium handcrafted wooden furniture made by traditional artisans in Chiniot, Punjab, Pakistan.`;

  // Comprehensive keywords combining page-specific and site keywords
  const allKeywords = [
    ...siteConfig.seo.keywords,
    ...keywords,
    ...tags,
    // Add contextual Chiniot keywords
    "furniture Chiniot Pakistan",
    "handcrafted wooden furniture",
    "traditional Pakistani furniture",
    "Punjab furniture makers"
  ];

  // Page URL and image
  const pageUrl = url ? getUrl(url) : getUrl();
  const pageImage = image ? getUrl(image) : getUrl("/og-image.jpg");
  const canonicalUrl = canonical ? getUrl(canonical) : pageUrl;

  return {
    title: pageTitle,
    description: pageDescription,
    keywords: allKeywords,
    authors: [{ name: author || siteConfig.name, url: getUrl() }],
    creator: siteConfig.name,
    publisher: siteConfig.name,
    
    // Robots configuration
    robots: {
      index: !noIndex,
      follow: !noIndex,
      googleBot: {
        index: !noIndex,
        follow: !noIndex,
        "max-image-preview": "large",
        "max-video-preview": -1,
        "max-snippet": -1,
      },
    },

    // Open Graph metadata
    openGraph: {
      type: type,
      locale: "en_US",
      url: pageUrl,
      siteName: siteConfig.name,
      title: pageTitle,
      description: pageDescription,
      images: [
        {
          url: pageImage,
          width: 1200,
          height: 630,
          alt: `${title || siteConfig.name} - Premium Chiniot Furniture`,
        },
      ],
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
      ...(author && { 
        authors: [author] 
      }),
      ...(section && { section }),
      ...(tags.length > 0 && { tags }),
    },

    // Twitter Card metadata
    twitter: {
      card: "summary_large_image",
      title: pageTitle,
      description: pageDescription,
      images: [pageImage],
      creator: "@chiniotiart",
      site: "@chiniotiart",
    },

    // Additional metadata for local SEO
    other: {
      // Geographic metadata
      "geo.region": "PK-PB",
      "geo.placename": "Chiniot, Punjab, Pakistan",
      "geo.position": `${siteConfig.seo.location.latitude};${siteConfig.seo.location.longitude}`,
      "ICBM": `${siteConfig.seo.location.latitude}, ${siteConfig.seo.location.longitude}`,
      
      // Business metadata
      "business:contact_data:street_address": siteConfig.contact.address.street,
      "business:contact_data:locality": siteConfig.contact.address.city,
      "business:contact_data:region": siteConfig.contact.address.region,
      "business:contact_data:postal_code": siteConfig.contact.address.postalCode,
      "business:contact_data:country_name": siteConfig.contact.address.country,
      "business:contact_data:phone_number": siteConfig.contact.phone,
      "business:contact_data:email": siteConfig.contact.email,
      
      // Content metadata
      "language": "English",
      "locale": "en_US",
      "rating": "General",
      "distribution": "Global",
      "revisit-after": "7 days",
      "copyright": `© ${new Date().getFullYear()} ${siteConfig.name}`,
      
      // Mobile optimization
      "format-detection": "telephone=yes, address=yes",
      "apple-mobile-web-app-capable": "yes",
      "apple-mobile-web-app-status-bar-style": "default",
      "apple-mobile-web-app-title": siteConfig.shortName,
      
      // Social media optimization
      "fb:app_id": process.env.NEXT_PUBLIC_FACEBOOK_APP_ID || "",
      "fb:pages": process.env.NEXT_PUBLIC_FACEBOOK_PAGE_ID || "",
      
      // Schema.org metadata
      "schema:name": pageTitle,
      "schema:description": pageDescription,
      "schema:image": pageImage,
      "schema:url": pageUrl,
      
      // Additional SEO tags
      "theme-color": siteConfig.theme.colors.primary,
      "msapplication-TileColor": siteConfig.theme.colors.primary,
      "msapplication-config": "/browserconfig.xml",
    },

    // Canonical URL
    alternates: {
      canonical: canonicalUrl,
      languages: {
        "en-US": pageUrl,
        "en": pageUrl,
      },
    },

    // Verification tags (add your verification codes)
    verification: {
      google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION || "",
      yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION || "",
      yahoo: process.env.NEXT_PUBLIC_YAHOO_VERIFICATION || "",
      other: {
        "msvalidate.01": process.env.NEXT_PUBLIC_BING_VERIFICATION || "",
        "facebook-domain-verification": process.env.NEXT_PUBLIC_FACEBOOK_VERIFICATION || "",
      },
    },

    // App-specific metadata
    applicationName: siteConfig.name,
    referrer: "origin-when-cross-origin",
    colorScheme: "light",
    
    // Category for app stores
    category: "shopping",
    
    // Classification
    classification: "Business",
  };
}

/**
 * Generate structured data for articles/blog posts
 */
export function generateArticleSchema({
  title,
  description,
  url,
  image,
  publishedTime,
  modifiedTime,
  author,
  section,
  tags = [],
}: {
  title: string;
  description: string;
  url: string;
  image?: string;
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
}) {
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    headline: title,
    description: description,
    image: image ? getUrl(image) : getUrl("/og-image.jpg"),
    url: getUrl(url),
    datePublished: publishedTime || new Date().toISOString(),
    dateModified: modifiedTime || new Date().toISOString(),
    author: {
      "@type": "Person",
      name: author || siteConfig.name,
      url: getUrl(),
    },
    publisher: {
      "@type": "Organization",
      name: siteConfig.name,
      logo: {
        "@type": "ImageObject",
        url: getUrl("/logo.svg"),
      },
    },
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": getUrl(url),
    },
    ...(section && { articleSection: section }),
    ...(tags.length > 0 && { keywords: tags.join(", ") }),
    inLanguage: "en-US",
    isPartOf: {
      "@type": "WebSite",
      name: siteConfig.name,
      url: getUrl(),
    },
    about: {
      "@type": "Thing",
      name: "Chiniot Furniture",
      description: "Traditional furniture craftsmanship from Chiniot, Punjab, Pakistan",
    },
  };
}

/**
 * Generate FAQ schema for better search results
 */
export function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqs.map((faq) => ({
      "@type": "Question",
      name: faq.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.answer,
      },
    })),
  };
}
