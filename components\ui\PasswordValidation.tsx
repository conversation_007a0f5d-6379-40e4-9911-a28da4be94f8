"use client";
import React from 'react';
import { PasswordCriteria } from '@/hooks/usePasswordValidation';

interface PasswordValidationProps {
  criteria: PasswordCriteria;
  show?: boolean;
  className?: string;
}

const PasswordValidation: React.FC<PasswordValidationProps> = ({ 
  criteria, 
  show = true, 
  className = "" 
}) => {
  if (!show) return null;

  const validationRules = [
    { key: 'minLength', label: 'At least 8 characters', met: criteria.minLength },
    { key: 'hasUppercase', label: 'Uppercase letter', met: criteria.hasUppercase },
    { key: 'hasLowercase', label: 'Lowercase letter', met: criteria.hasLowercase },
    { key: 'hasNumber', label: 'Number', met: criteria.hasNumber },
    { key: 'hasSpecial', label: 'Special character', met: criteria.hasSpecial },
  ];

  return (
    <div className={`mt-2 space-y-1 ${className}`}>
      <p className="text-xs font-medium mb-1">Password must contain:</p>
      <div className="grid grid-cols-2 gap-1">
        {validationRules.map((rule) => (
          <div
            key={rule.key}
            className={`text-xs flex items-center ${
              rule.met ? 'text-green-600' : 'text-muted-foreground'
            }`}
          >
            <div
              className={`mr-1 w-3 h-3 rounded-full ${
                rule.met ? 'bg-green-600' : 'bg-muted'
              }`}
            />
            {rule.label}
          </div>
        ))}
      </div>
    </div>
  );
};

export default PasswordValidation;
