"use client";
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import ProductCard from "@/components/ProductCard";
import { getProducts } from "@/apis/products";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

interface ProductsSectionProps {
  title: string;
  subtitle?: string;
  limit?: number;
  filter?: string; // "featured", "trending", "new", etc.
  headingLevel?: "h2" | "h3" | "h4"; // For proper heading hierarchy
  sectionId?: string; // For accessibility and SEO
}

const ProductsSection: React.FC<ProductsSectionProps> = ({
  title,
  subtitle,
  limit = 8,
  filter,
  headingLevel = "h2",
  sectionId,
}) => {
  const [products, setProducts] = useState<ProductData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const fetchedProducts = await getProducts(filter);
        setProducts(fetchedProducts.slice(0, limit));
      } catch (error) {
        console.error("Error fetching products:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [filter, limit]);

  // Dynamic heading component for proper hierarchy
  const HeadingComponent = headingLevel;
  const headingId = sectionId ? `${sectionId}-heading` : `${title.toLowerCase().replace(/\s+/g, '-')}-heading`;

  return (
    <section
      className="py-12 px-4 md:px-8"
      aria-labelledby={headingId}
      id={sectionId}
    >
      <div className="container mx-auto">
        {/* Section header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <HeadingComponent
                id={headingId}
                className="text-2xl md:text-3xl font-bold mb-2"
              >
                {title}
              </HeadingComponent>
            </motion.div>
            {subtitle && (
              <motion.p
                className="text-gray-600 max-w-2xl"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                {subtitle}
              </motion.p>
            )}
          </div>

          <Link
            href="/products"
            className="group hidden md:flex items-center text-accent font-medium mt-4 md:mt-0 hover:text-accent/80 transition-colors"
            aria-label={`View all ${title.toLowerCase()} products`}
          >
            View All Chiniot Furniture
            <motion.span
              className="inline-block ml-1"
              initial={{ x: 0 }}
              whileHover={{ x: 5 }}
              transition={{ type: "spring", stiffness: 400 }}
            >
              <ArrowRight size={16} />
            </motion.span>
          </Link>
        </div>

        {/* Products grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, index) => (
              <div
                key={index}
                className="bg-gray-100 rounded-lg h-80 animate-pulse"
              ></div>
            ))}
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
          >
            {products.length > 0 ? (
              products.map((product, index) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  index={index}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <p className="text-gray-500">
                  No products found.
                </p>
              </div>
            )}
          </motion.div>
        )}

        {/* Mobile view all link */}
        <div className="mt-8 text-center md:hidden">
          <Link
            href="/products"
            className="inline-flex items-center text-accent font-medium"
          >
            View All Products
            <ArrowRight size={16} className="ml-1" />
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ProductsSection;
