"use client";

import React from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import BreadcrumbJsonLd from "@/components/SEO/BreadcrumbJsonLd";
import LocalSEOSchema from "@/components/SEO/LocalSEOSchema";
import FAQSchema, { chiniotFurnitureFAQs } from "@/components/SEO/FAQSchema";
import Script from "next/script";
import { siteConfig, getUrl } from "@/config/site";
import { MapPin, Award, Users, Clock, Star } from "lucide-react";

export default function ChiniotFurniturePage() {
  // Breadcrumb for this page
  const breadcrumbs = [
    { name: "Chiniot Furniture", url: "/chiniot-furniture", description: "Learn about authentic Chiniot furniture heritage and craftsmanship" }
  ];

  // Page schema for local SEO
  const pageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "@id": `${getUrl("/chiniot-furniture")}/#webpage`,
    name: "Chiniot Furniture - Traditional Pakistani Craftsmanship",
    description: "Discover the rich heritage of Chiniot furniture from Punjab, Pakistan. Learn about traditional woodworking techniques, authentic craftsmanship, and the history of the world's furniture capital.",
    url: getUrl("/chiniot-furniture"),
    mainEntity: {
      "@type": "Place",
      name: "Chiniot",
      description: "Historic city in Punjab, Pakistan, renowned worldwide for wooden furniture manufacturing",
      geo: {
        "@type": "GeoCoordinates",
        latitude: siteConfig.seo.location.latitude,
        longitude: siteConfig.seo.location.longitude
      },
      containedInPlace: {
        "@type": "State",
        name: "Punjab",
        containedInPlace: {
          "@type": "Country",
          name: "Pakistan"
        }
      },
      sameAs: "https://en.wikipedia.org/wiki/Chiniot"
    },
    about: {
      "@type": "Thing",
      name: "Chiniot Furniture Tradition",
      description: "500+ years of traditional wooden furniture craftsmanship from Chiniot, Punjab, Pakistan"
    },
    keywords: "Chiniot furniture, Pakistani furniture, traditional craftsmanship, wooden furniture Pakistan, Chiniot artisans, furniture heritage, Punjab furniture, handcrafted furniture"
  };

  const features = [
    {
      icon: <Clock className="h-8 w-8 text-accent" />,
      title: "500+ Years Heritage",
      description: "Chiniot's furniture tradition spans over five centuries, with techniques passed down through generations of master craftsmen."
    },
    {
      icon: <Award className="h-8 w-8 text-accent" />,
      title: "World Recognition",
      description: "Chiniot is globally recognized as the furniture capital of the world, with its products gracing homes and palaces internationally."
    },
    {
      icon: <Users className="h-8 w-8 text-accent" />,
      title: "Master Artisans",
      description: "Thousands of skilled craftsmen in Chiniot continue the ancient art of wood carving and furniture making with unmatched expertise."
    },
    {
      icon: <Star className="h-8 w-8 text-accent" />,
      title: "Premium Quality",
      description: "Using the finest Pakistani hardwoods like Sheesham and Teak, Chiniot furniture is built to last for generations."
    }
  ];

  const stats = [
    { number: "500+", label: "Years of Tradition" },
    { number: "10,000+", label: "Skilled Artisans" },
    { number: "100+", label: "Countries Served" },
    { number: "1M+", label: "Pieces Crafted" }
  ];

  return (
    <>
      {/* SEO Structured Data */}
      <BreadcrumbJsonLd 
        items={breadcrumbs}
        currentPage="Chiniot Furniture Heritage"
      />
      
      <LocalSEOSchema 
        businessType="FurnitureStore"
        additionalServices={["Custom Design", "Restoration", "International Shipping"]}
        serviceArea={["Punjab", "Pakistan", "International"]}
        nearbyLandmarks={["Chiniot Bridge", "Shahi Mosque", "Umar Hayat Palace"]}
      />
      
      <Script
        id="chiniot-page-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(pageSchema)
        }}
      />

      <FAQSchema 
        faqs={chiniotFurnitureFAQs}
        title="Chiniot Furniture - Frequently Asked Questions"
        description="Learn about Chiniot furniture heritage, craftsmanship, and traditions"
      />

      <main className="min-h-screen">
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-br from-amber-50 to-orange-50">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center max-w-4xl mx-auto"
            >
              <div className="flex items-center justify-center mb-4">
                <MapPin className="h-6 w-6 text-accent mr-2" />
                <span className="text-accent font-semibold">Chiniot, Punjab, Pakistan</span>
              </div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Authentic <span className="text-accent">Chiniot Furniture</span>
              </h1>
              <p className="text-lg md:text-xl text-gray-600 mb-8">
                Discover the rich heritage of Chiniot - the world's furniture capital. For over 500 years, 
                skilled artisans in this historic Pakistani city have been creating masterpieces that blend 
                traditional craftsmanship with timeless beauty.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/products">
                  <Button size="lg" className="bg-accent hover:bg-accent/90">
                    Explore Chiniot Collection
                  </Button>
                </Link>
                <Link href="/contact">
                  <Button variant="outline" size="lg">
                    Custom Orders
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Heritage Story Section */}
        <section className="py-16" aria-labelledby="heritage-heading">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <h2 id="heritage-heading" className="text-3xl md:text-4xl font-bold mb-6">
                  The Heritage of Chiniot Craftsmanship
                </h2>
                <p className="text-gray-600 mb-6">
                  Chiniot, a historic city in Punjab, Pakistan, has been the epicenter of wooden furniture 
                  craftsmanship for over 500 years. The city's artisans have created furniture for Mughal 
                  emperors, British colonial administrators, and modern homes worldwide.
                </p>
                <p className="text-gray-600 mb-6">
                  The traditional techniques of Chiniot furniture making include intricate wood carving, 
                  joinery without nails, and hand-finishing that creates pieces of unparalleled beauty 
                  and durability. Each piece tells a story of dedication, skill, and cultural heritage.
                </p>
                <div className="bg-amber-50 p-6 rounded-lg">
                  <h3 className="font-semibold text-lg mb-2">Did You Know?</h3>
                  <p className="text-gray-700 text-sm">
                    Chiniot furniture is so renowned that the city is often called the "Manchester of Pakistan" 
                    for furniture, with its products exported to over 100 countries worldwide.
                  </p>
                </div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="relative"
              >
                <Image
                  src="/assets/backgrounds/home-bg.png"
                  alt="Traditional Chiniot furniture craftsman creating handcrafted wooden furniture using 500-year-old techniques in Chiniot, Punjab, Pakistan"
                  width={600}
                  height={400}
                  className="rounded-lg shadow-lg"
                />
                <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white p-3 rounded">
                  <p className="text-sm font-semibold">Master craftsman in Chiniot workshop</p>
                  <p className="text-xs">Traditional techniques passed down through generations</p>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-gray-50" aria-labelledby="features-heading">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 id="features-heading" className="text-3xl md:text-4xl font-bold mb-4">
                What Makes Chiniot Furniture Special
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Discover the unique characteristics that make Chiniot furniture the most sought-after 
                handcrafted wooden furniture in the world.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Card className="h-full text-center hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex justify-center mb-4">
                        {feature.icon}
                      </div>
                      <CardTitle className="text-xl">{feature.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-600">{feature.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Statistics Section */}
        <section className="py-16 bg-accent text-white" aria-labelledby="stats-heading">
          <div className="container mx-auto px-4 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-12"
            >
              <h2 id="stats-heading" className="text-3xl md:text-4xl font-bold mb-4">
                Chiniot by Numbers
              </h2>
              <p className="text-lg opacity-90 max-w-2xl mx-auto">
                The impressive scale and reach of Chiniot's furniture industry
              </p>
            </motion.div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="text-3xl md:text-4xl font-bold mb-2">{stat.number}</div>
                  <div className="text-sm md:text-base opacity-90">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16">
          <div className="container mx-auto px-4 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="max-w-3xl mx-auto"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Experience Authentic Chiniot Craftsmanship
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Bring the timeless beauty and exceptional quality of traditional Chiniot furniture 
                to your home. Each piece is a testament to centuries of craftsmanship excellence.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/products">
                  <Button size="lg" className="bg-accent hover:bg-accent/90">
                    Shop Chiniot Furniture
                  </Button>
                </Link>
                <Link href="/about-us">
                  <Button variant="outline" size="lg">
                    Learn Our Story
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>
    </>
  );
}
