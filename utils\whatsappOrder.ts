import { Order } from '@/types/order';
import { Cart } from '@/types/cart';
import { siteConfig } from '@/config/site';
import { CURRENCY_CONFIG, CurrencyCode } from '@/constants/helpers';

export interface WhatsAppOrderData {
  cart?: Cart;
  order?: Order;
  customerDetails: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address: string;
    city: string;
    postalCode?: string;
  };
  formatPrice?: (price: number) => string;
  selectedCurrency?: CurrencyCode;
}

/**
 * Formats cart or order data into a WhatsApp message
 */
export const formatWhatsAppOrderMessage = (data: WhatsAppOrderData): string => {
  const { cart, order, customerDetails, formatPrice, selectedCurrency = 'USD' } = data;
  
  // Use cart if available, otherwise use order
  const items = cart?.items || order?.items || [];
  const total = cart?.total || order?.total || 0;
  const subtotal = cart?.subtotal || order?.subtotal || 0;
  const discount = cart?.discount || order?.discount || 0;
  
  // Currency conversion and formatting functions
  const convertPrice = (price: number, fromCurrency: CurrencyCode = 'USD', toCurrency: CurrencyCode = selectedCurrency): number => {
    if (fromCurrency === toCurrency) return price;
    
    // Convert to USD first (base currency)
    const priceInUSD = price / CURRENCY_CONFIG[fromCurrency].rate;
    
    // Convert from USD to target currency
    const convertedPrice = priceInUSD * CURRENCY_CONFIG[toCurrency].rate;
    
    return Math.round(convertedPrice);
  };

  const defaultFormatPrice = (price: number): string => {
    const convertedPrice = convertPrice(price, 'USD', selectedCurrency);
    const currencyInfo = CURRENCY_CONFIG[selectedCurrency];
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: selectedCurrency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(convertedPrice);
  };
  
  // Use provided formatter or default currency-aware formatter
  const priceFormatter = formatPrice || defaultFormatPrice;
  
  // Build the message with greeting
  let message = `👋 *Hello! Greetings from Chinioti Wooden Art*\n\n`;
  message += `I hope you're having a wonderful day! I'm interested in placing an order for some beautiful wooden art pieces. Here are the details:\n\n`;
  message += `🛒 *Order Details*\n\n`;
  
  // Customer Information
  message += `👤 *Customer Details:*\n`;
  message += `• Name: ${customerDetails.firstName} ${customerDetails.lastName}\n`;
  message += `• Email: ${customerDetails.email}\n`;
  message += `• Phone: ${customerDetails.phone}\n`;
  message += `• Address: ${customerDetails.address}, ${customerDetails.city}`;
  if (customerDetails.postalCode) {
    message += `, ${customerDetails.postalCode}`;
  }
  message += `\n\n`;
  
  // Order Items
  message += `📦 *Order Items:*\n`;
  items.forEach((item, index) => {
    const productName = item.product?.title || item.product?.name || 'Unknown Product';
    const price = item.product?.price || 0;
    const quantity = item.quantity || 1;
    const itemTotal = price * quantity;
    
    // Get product image - try images array first, then single image property
    const productImage = item.product?.images?.[0] || item.product?.image || null;
    
    message += `${index + 1}. ${productName}\n`;
    message += `   • Quantity: ${quantity}\n`;
    message += `   • Unit Price: ${priceFormatter(price)}\n`;
    message += `   • Total: ${priceFormatter(itemTotal)}\n`;
    
    // Add product image if available
    if (productImage) {
      message += `   • Image: ${productImage}\n`;
    }
    
    message += `\n`;
  });
  
  // Order Summary
  message += `💰 *Order Summary:*\n`;
  message += `• Subtotal: ${priceFormatter(subtotal)}\n`;
  if (discount > 0) {
    message += `• Discount: -${priceFormatter(discount)}\n`;
  }
  message += `• *Total: ${priceFormatter(total)}*\n\n`;
  
  // Product Images Section
  const productImages = items
    .map((item, index) => ({
      name: item.product?.title || item.product?.name || `Product ${index + 1}`,
      image: item.product?.images?.[0] || item.product?.image
    }))
    .filter(item => item.image);
    
  if (productImages.length > 0) {
    message += `🖼️ *Product Images:*\n`;
    productImages.forEach((item, index) => {
      message += `${index + 1}. ${item.name}: ${item.image}\n`;
    });
    message += `\n`;
  }
  
  // Footer
  message += `📞 Please confirm this order and let me know the delivery details.\n`;
  message += `Thank you! 🙏`;
  
  return message;
};

/**
 * Creates a WhatsApp URL with the formatted order message
 */
export const createWhatsAppOrderUrl = (data: WhatsAppOrderData): string => {
  const message = formatWhatsAppOrderMessage(data);
  const phoneNumber = siteConfig.contact.whatsapp;
  
  // Format phone number (remove + and any spaces)
  const formattedPhoneNumber = phoneNumber.replace(/[\s+]/g, '');
  
  // Create WhatsApp URL
  const whatsappUrl = `https://wa.me/${formattedPhoneNumber}?text=${encodeURIComponent(message)}`;
  
  return whatsappUrl;
};

/**
 * Opens WhatsApp with the order details
 */
export const sendOrderViaWhatsApp = (data: WhatsAppOrderData): void => {
  const whatsappUrl = createWhatsAppOrderUrl(data);
  window.open(whatsappUrl, '_blank');
};
