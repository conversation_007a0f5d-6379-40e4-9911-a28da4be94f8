import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { Poppins } from "next/font/google";
import "./globals.css";
import { Toaster } from "sonner";
import { AuthProvider } from "@/contexts/AuthContext";
import { CurrencyProvider } from "@/contexts/CurrencyContext";
import { siteConfig, getUrl } from "@/config/site";
import EnhancedSEO from "@/components/SEO/EnhancedSEO";

const poppins = Poppins({
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  display: "swap",
  fallback: ["system-ui", "Arial", "sans-serif"],
  preload: false,
  variable: "--font-poppins",
});

// Define viewport metadata
export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  themeColor: siteConfig.theme.colors.primary,
};

// Enhanced metadata for the site with comprehensive SEO
export const metadata: Metadata = {
  metadataBase: new URL(getUrl()),
  title: {
    default: `${siteConfig.name} - Premium Handcrafted Furniture from Chiniot, Pakistan`,
    template: `%s | ${siteConfig.name} - Authentic Chiniot Furniture`,
  },
  description: `${siteConfig.description} Discover authentic handcrafted wooden furniture made by skilled artisans in Chiniot, Punjab, Pakistan. Premium quality beds, tables, chairs, wardrobes and more.`,
  keywords: siteConfig.seo.keywords,
  authors: [{ name: siteConfig.name, url: getUrl() }],
  creator: siteConfig.name,
  publisher: siteConfig.name,

  // Enhanced format detection
  formatDetection: {
    email: true,
    address: true,
    telephone: true,
  },

  // Comprehensive robots configuration
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-image-preview": "large",
      "max-video-preview": -1,
      "max-snippet": -1,
    },
  },

  // Enhanced Open Graph with local business context
  openGraph: {
    type: "website",
    locale: "en_US",
    url: getUrl(),
    siteName: siteConfig.name,
    title: `${siteConfig.name} - Premium Handcrafted Furniture from Chiniot, Pakistan`,
    description: `${siteConfig.description} Discover authentic handcrafted wooden furniture made by skilled artisans in Chiniot, Punjab, Pakistan.`,
    images: [
      {
        url: getUrl("/og-image.jpg"),
        width: 1200,
        height: 630,
        alt: `${siteConfig.name} - Premium Chiniot Furniture from Pakistan`,
      },
      {
        url: getUrl("/og-image.svg"),
        width: 1200,
        height: 630,
        alt: `${siteConfig.name} - Handcrafted Wooden Furniture`,
      },
    ],
  },

  // Enhanced Twitter Card
  twitter: {
    card: "summary_large_image",
    title: `${siteConfig.name} - Premium Chiniot Furniture from Pakistan`,
    description: `${siteConfig.description} Authentic handcrafted wooden furniture from Chiniot artisans.`,
    images: [getUrl("/twitter-image.jpg")],
    creator: "@chiniotiart",
    site: "@chiniotiart",
  },

  // Comprehensive icons and app metadata
  icons: {
    icon: [
      { url: "/favicon.ico", sizes: "any" },
      { url: "/favicon.svg", type: "image/svg+xml" },
    ],
    apple: [
      { url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" },
    ],
    other: [
      {
        rel: "mask-icon",
        url: "/safari-pinned-tab.svg",
        color: siteConfig.theme.colors.primary,
      },
    ],
  },

  // PWA manifest
  manifest: "/manifest.json",

  // Enhanced alternates with hreflang
  alternates: {
    canonical: getUrl(),
    languages: {
      "en-US": getUrl(),
      "en": getUrl(),
    },
  },

  // App-specific metadata
  applicationName: siteConfig.name,
  referrer: "origin-when-cross-origin",
  colorScheme: "light",

  // Verification tags (add your actual verification codes in environment variables)
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION || "",
    yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION || "",
    yahoo: process.env.NEXT_PUBLIC_YAHOO_VERIFICATION || "",
    other: {
      "msvalidate.01": process.env.NEXT_PUBLIC_BING_VERIFICATION || "",
      "facebook-domain-verification": process.env.NEXT_PUBLIC_FACEBOOK_VERIFICATION || "",
    },
  },

  // Additional metadata for local SEO
  other: {
    "geo.region": "PK-PB",
    "geo.placename": "Chiniot, Punjab, Pakistan",
    "geo.position": `${siteConfig.seo.location.latitude};${siteConfig.seo.location.longitude}`,
    "ICBM": `${siteConfig.seo.location.latitude}, ${siteConfig.seo.location.longitude}`,
    "business:contact_data:street_address": siteConfig.contact.address.street,
    "business:contact_data:locality": siteConfig.contact.address.city,
    "business:contact_data:region": siteConfig.contact.address.region,
    "business:contact_data:postal_code": siteConfig.contact.address.postalCode,
    "business:contact_data:country_name": siteConfig.contact.address.country,
    "business:contact_data:phone_number": siteConfig.contact.phone,
    "business:contact_data:email": siteConfig.contact.email,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={poppins.className}>
      <head>
        {/* Enhanced SEO Components */}
        <EnhancedSEO />

        {/* Favicon and App Icons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />

        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://www.google-analytics.com" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />

        {/* DNS prefetch for better performance */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />
        <link rel="dns-prefetch" href="//www.google-analytics.com" />

        {/* Additional meta tags for mobile optimization */}
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content={siteConfig.shortName} />
        <meta name="msapplication-TileColor" content={siteConfig.theme.colors.primary} />
        <meta name="theme-color" content={siteConfig.theme.colors.primary} />
      </head>
      <body className="uppercase antialiased bg-background text-foreground font-sans">
        <AuthProvider>
          <CurrencyProvider>
            {children}
            <Toaster
              position="top-right"
              richColors
              closeButton
              expand={false}
              visibleToasts={5}
              duration={4000}
              toastOptions={{
                style: {
                  marginTop: '30px',
                },
              }}
            />
          </CurrencyProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
