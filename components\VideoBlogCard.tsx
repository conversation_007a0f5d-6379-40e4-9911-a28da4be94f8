"use client";

import React, { useState, useRef } from 'react';
import { VideoBlogData, formatDuration } from '@/apis/videoBlogs';
import { Card, CardContent } from '@/components/ui/card';
import { Play, Pause, Volume2, VolumeX, Maximize, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Image from 'next/image';

interface VideoBlogCardProps {
  videoBlog: VideoBlogData;
  autoPlay?: boolean;
  showControls?: boolean;
  className?: string;
}

const VideoBlogCard: React.FC<VideoBlogCardProps> = ({
  videoBlog,
  autoPlay = false,
  showControls = true,
  className = ""
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [showVideo, setShowVideo] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleMuteToggle = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const handleFullscreen = () => {
    if (videoRef.current) {
      if (videoRef.current.requestFullscreen) {
        videoRef.current.requestFullscreen();
      }
    }
  };

  const handleThumbnailClick = () => {
    setShowVideo(true);
    setIsLoading(true);
  };

  const handleVideoLoad = () => {
    setIsLoading(false);
    if (autoPlay && videoRef.current) {
      videoRef.current.play();
      setIsPlaying(true);
    }
  };

  const handleVideoEnded = () => {
    setIsPlaying(false);
  };

  const handleVideoError = () => {
    setIsLoading(false);
    console.error('Error loading video:', videoBlog.title);
  };

  return (
    <Card className={`group overflow-hidden transition-all duration-300 hover:shadow-lg ${className}`}>
      <CardContent className="p-0">
        <div className="relative aspect-video bg-gray-100 overflow-hidden">
          {!showVideo ? (
            // Thumbnail view
            <div 
              className="relative w-full h-full cursor-pointer group"
              onClick={handleThumbnailClick}
            >
              {videoBlog.thumbnailUrl ? (
                <Image
                  src={videoBlog.thumbnailUrl}
                  alt={videoBlog.title}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                  <Play className="w-16 h-16 text-gray-500" />
                </div>
              )}
              
              {/* Play overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                <div className="bg-white bg-opacity-90 rounded-full p-4 transform scale-0 group-hover:scale-100 transition-transform duration-300">
                  <Play className="w-8 h-8 text-gray-800 ml-1" />
                </div>
              </div>

              {/* Duration badge */}
              {videoBlog.duration && (
                <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                  {formatDuration(videoBlog.duration)}
                </div>
              )}

              {/* Views badge */}
              <div className="absolute top-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
                <Eye className="w-3 h-3" />
                {videoBlog.views.toLocaleString()}
              </div>
            </div>
          ) : (
            // Video player view
            <div className="relative w-full h-full">
              {isLoading && (
                <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              )}
              
              <video
                ref={videoRef}
                className="w-full h-full object-cover"
                muted={isMuted}
                onLoadedData={handleVideoLoad}
                onEnded={handleVideoEnded}
                onError={handleVideoError}
                preload="metadata"
              >
                <source src={videoBlog.videoUrl} type={videoBlog.mimetype} />
                Your browser does not support the video tag.
              </video>

              {/* Video controls overlay */}
              {showControls && (
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-white hover:bg-white/20"
                        onClick={handlePlayPause}
                      >
                        {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-white hover:bg-white/20"
                        onClick={handleMuteToggle}
                      >
                        {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                      </Button>
                    </div>

                    <Button
                      size="sm"
                      variant="ghost"
                      className="text-white hover:bg-white/20"
                      onClick={handleFullscreen}
                    >
                      <Maximize className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Video title */}
        <div className="p-4">
          <h3 className="font-semibold text-lg text-gray-900 line-clamp-2 leading-tight">
            {videoBlog.title}
          </h3>
          
          <div className="mt-2 flex items-center justify-between text-sm text-gray-500">
            <span>{new Date(videoBlog.createdAt).toLocaleDateString()}</span>
            <span>{videoBlog.views.toLocaleString()} views</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default VideoBlogCard;
