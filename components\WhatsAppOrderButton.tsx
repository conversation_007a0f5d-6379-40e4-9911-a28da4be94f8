'use client';
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaWhatsapp } from 'react-icons/fa';
import { Button } from '@/components/ui/button';
import { sendOrderViaWhatsApp, WhatsAppOrderData } from '@/utils/whatsappOrder';

interface WhatsAppOrderButtonProps {
  orderData?: WhatsAppOrderData;
  onOrderCreate?: () => Promise<WhatsAppOrderData>;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'lg' | 'default' | 'icon';
  className?: string;
  children?: React.ReactNode;
  disabled?: boolean;
  fullWidth?: boolean;
}

const WhatsAppOrderButton: React.FC<WhatsAppOrderButtonProps> = ({
  orderData,
  onOrderCreate,
  variant = 'default',
  size = 'default',
  className = '',
  children,
  disabled = false,
  fullWidth = false,
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleWhatsAppOrder = async () => {
    if (disabled || isProcessing) return;
    
    try {
      setIsProcessing(true);
      let finalOrderData = orderData;
      
      // If onOrderCreate is provided, call it to create the order first
      if (onOrderCreate) {
        finalOrderData = await onOrderCreate();
      }
      
      // Only proceed if we have order data
      if (finalOrderData) {
        sendOrderViaWhatsApp(finalOrderData);
      }
    } catch (error) {
      console.error('Error handling WhatsApp order:', error);
      // Error handling is done in the onOrderCreate function
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <motion.div
      whileHover={{ scale: disabled ? 1 : 1.02 }}
      whileTap={{ scale: disabled ? 1 : 0.98 }}
      className={fullWidth ? 'w-full' : ''}
    >
      <Button
        onClick={handleWhatsAppOrder}
        variant={variant}
        size={size}
        disabled={disabled || isProcessing}
        className={`
          bg-green-500 hover:bg-green-600 text-white border-green-500 hover:border-green-600
          flex items-center justify-center gap-2 transition-all duration-200
          ${fullWidth ? 'w-full' : ''}
          ${disabled || isProcessing ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${className}
        `}
      >
        <FaWhatsapp size={18} />
        {isProcessing ? 'Processing...' : (children || 'Order via WhatsApp')}
      </Button>
    </motion.div>
  );
};

export default WhatsAppOrderButton;
