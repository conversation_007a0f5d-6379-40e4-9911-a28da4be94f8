"use client";

import <PERSON><PERSON><PERSON> from "next/script";
import { getUrl, siteConfig } from "@/config/site";

interface BreadcrumbItem {
  name: string;
  url: string;
  description?: string;
}

interface BreadcrumbJsonLdProps {
  items: BreadcrumbItem[];
  currentPage?: string;
  baseUrl?: string;
}

/**
 * Enhanced Breadcrumb structured data component for better navigation SEO
 * Includes additional metadata for improved search result display
 */
const BreadcrumbJsonLd = ({ items, currentPage, baseUrl }: BreadcrumbJsonLdProps) => {
  if (!items || items.length === 0) return null;

  const siteBaseUrl = baseUrl || getUrl();

  // Always include home as the first item if not present
  const breadcrumbItems = items[0]?.name !== "Home"
    ? [{ name: "Home", url: "/", description: "Chinioti Wooden Art - Premium Furniture from Chiniot" }, ...items]
    : items;

  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    name: "Navigation Breadcrumb",
    description: `Navigation path for ${currentPage || "current page"} on ${siteConfig.name}`,
    itemListElement: breadcrumbItems.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: {
        "@type": "WebPage",
        "@id": item.url.startsWith('http') ? item.url : `${siteBaseUrl}${item.url}`,
        name: item.name,
        url: item.url.startsWith('http') ? item.url : `${siteBaseUrl}${item.url}`,
        ...(item.description && { description: item.description }),
        isPartOf: {
          "@type": "WebSite",
          name: siteConfig.name,
          url: siteBaseUrl,
        },
      },
    })),
    numberOfItems: breadcrumbItems.length,
  };

  return (
    <Script
      id="breadcrumb-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(breadcrumbSchema),
      }}
    />
  );
};

// Helper function to generate common breadcrumb paths
export function generateBreadcrumbs(path: string, customItems?: BreadcrumbItem[]): BreadcrumbItem[] {
  if (customItems) return customItems;

  const pathSegments = path.split('/').filter(Boolean);
  const breadcrumbs: BreadcrumbItem[] = [];

  let currentPath = '';

  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`;

    // Convert segment to readable name
    let name = segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    // Special cases for common paths
    switch (segment) {
      case 'products':
        name = 'Products';
        break;
      case 'categories':
        name = 'Categories';
        break;
      case 'about-us':
        name = 'About Us';
        break;
      case 'contact':
        name = 'Contact';
        break;
      case 'video-blogs':
        name = 'VLOGS';
        break;
      case 'blogs':
        name = 'Blog';
        break;
      case 'privacy-policy':
        name = 'Privacy Policy';
        break;
      case 'terms-of-service':
        name = 'Terms of Service';
        break;
    }

    breadcrumbs.push({
      name,
      url: currentPath,
      description: `${name} - ${siteConfig.name}`,
    });
  });

  return breadcrumbs;
}

// Common breadcrumb patterns for the furniture website
export const commonBreadcrumbs = {
  products: [
    { name: "Products", url: "/products", description: "Browse our collection of handcrafted Chiniot furniture" }
  ],
  categories: [
    { name: "Categories", url: "/categories", description: "Furniture categories from Chiniot artisans" }
  ],
  aboutUs: [
    { name: "About Us", url: "/about-us", description: "Learn about our Chiniot furniture heritage and craftsmanship" }
  ],
  contact: [
    { name: "Contact", url: "/contact", description: "Get in touch with Chinioti Wooden Art" }
  ],
  blogs: [
    { name: "Blog", url: "/blogs", description: "Articles about Chiniot furniture and craftsmanship" }
  ],
  videoBlogs: [
    { name: "VLOGS", url: "/video-blogs", description: "Watch our furniture making process and tutorials" }
  ],
};

export default BreadcrumbJsonLd;
