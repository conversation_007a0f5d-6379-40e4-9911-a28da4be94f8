'use client';

import React from 'react';
import { useCurrency } from '@/contexts/CurrencyContext';
import { CurrencyCode } from '@/constants/helpers';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown, DollarSign } from 'lucide-react';

interface CurrencySelectorProps {
  className?: string;
  variant?: 'default' | 'compact';
}

const CurrencySelector: React.FC<CurrencySelectorProps> = ({ 
  className = '', 
  variant = 'default' 
}) => {
  const { selectedCurrency, setCurrency, getAllCurrencies } = useCurrency();
  const currencies = getAllCurrencies();

  const handleCurrencyChange = (currency: CurrencyCode) => {
    setCurrency(currency);
  };

  if (variant === 'compact') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={`h-9 sm:h-8 px-2 sm:px-2 text-xs min-w-[44px] flex items-center justify-center ${className}`}
          >
            <DollarSign size={14} className="sm:w-3.5 sm:h-3.5 mr-1" />
            <span className="hidden sm:inline text-xs">{currencies[selectedCurrency].code}</span>
            <span className="sm:hidden text-xs">{currencies[selectedCurrency].symbol}</span>
            <ChevronDown size={12} className="sm:w-3 sm:h-3 ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-32 sm:w-36 space-y-1 p-1 z-[60]">
          {Object.entries(currencies).map(([code, currency]) => (
            <DropdownMenuItem
              key={code}
              onClick={() => handleCurrencyChange(code as CurrencyCode)}
              className={`cursor-pointer mb-1 last:mb-0 p-2 rounded-sm ${
                selectedCurrency === code ? 'bg-accent text-white' : 'hover:bg-gray-100'
              }`}
            >
              <span className="text-xs">
                {currency.symbol} {currency.code}
              </span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className={`flex items-center gap-4 ${className}`}
        >
          <DollarSign size={16} />
          <span>{currencies[selectedCurrency].code}</span>
          <span className="text-sm text-gray-500">
            ({currencies[selectedCurrency].symbol})
          </span>
          <ChevronDown size={16} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48 space-y-1 p-1 z-[60]">
        {Object.entries(currencies).map(([code, currency]) => (
          <DropdownMenuItem
            key={code}
            onClick={() => handleCurrencyChange(code as CurrencyCode)}
            className={`cursor-pointer flex items-center gap-2 justify-between mb-1 last:mb-0 ${
              selectedCurrency === code ? 'bg-accent' : ''
            }`}
          >
            <div className="flex items-center gap-4">
              <span className="font-medium">{currency.symbol}</span>
              <span>{currency.code}</span>
            </div>
            <span className="text-sm text-gray-500">{currency.name}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default CurrencySelector;
