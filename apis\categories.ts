export const getCategories = () => {
  const data: Category[] = [
    {
      id: "sofa-001",
      image: ["/assets/product_images/p(1).png"],
      name: "<PERSON><PERSON><PERSON>",
    },
    {
      id: "bed-001",
      image: ["/assets/product_images/p(3).png"],
      name: "Beds",
    },
    {
      id: "chair-001",
      image: ["/assets/product_images/p(4).png"],
      name: "Chairs",
    },
    {
      id: "table-001",
      image: ["/assets/product_images/p(4).png"],
      name: "Tables",
    },
    {
      id: "swing-001",
      image: ["/assets/product_images/p(4).png"],
      name: "Swing<PERSON>",
    },
  ];

  return data;
};

export const getFeaturedData = () => {
  const data: FeatureCardProps[] = [
    {
      image:
        "https://cdn.sanity.io/images/c9kbt96b/production/f9a5b990db9f4563a56ed102a8568acfa72bbf99-4000x2666.jpg?rect=0,997,4000,1669&w=750&q=75&fit=clip&auto=format",
      title: "L'Appartamento by Artemest",
      desc: "For the Milano Design Week 2025 Artemest presents the third edition of L'Appartamento, a showhouse in a stunning Milanese palazzo in Via Gaetano Donizetti 48.",
      button_text: "Shop the Collection",
      link: "",
    },
    {
      image:
        "https://cdn.sanity.io/images/c9kbt96b/production/f9a5b990db9f4563a56ed102a8568acfa72bbf99-4000x2666.jpg?rect=0,997,4000,1669&w=750&q=75&fit=clip&auto=format",
      title: "Sculpted in Stone: Marble Lighting Masterpieces",
      desc: "Experience the enduring allure of marble lighting, where the organic beauty of natural veining meets refined sculptural design. From minimalist table lamps to d",
      button_text: "Shop the Collection",
      link: "",
    },
  ];
  return data;
};
