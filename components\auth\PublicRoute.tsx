"use client";

import React, { ReactNode, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";

interface PublicRouteProps {
  children: ReactNode;
  redirectTo?: string;
}

const PublicRoute: React.FC<PublicRouteProps> = ({
  children,
  redirectTo = "/",
}) => {
  return (
    <Suspense fallback={<div></div>}>
      <PublicRouteContent children={children} redirectTo={redirectTo} />
    </Suspense>
  );
};

const PublicRouteContent: React.FC<PublicRouteProps> = ({
  children,
  redirectTo = "/",
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectPath = searchParams.get("redirect");

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push(redirectPath || redirectTo);
    }
  }, [isAuthenticated, isLoading, router, redirectTo, redirectPath]);

  // Don't show any loading state - let the signin/signup buttons handle their own loading
  if (isLoading) {
    return <>{children}</>;
  }

  if (isAuthenticated) {
    return null
  }

  return <>{children}</>;
};

export default PublicRoute;
