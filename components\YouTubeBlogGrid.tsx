"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { getVideoBlogs, getVideoBlogCategories, VideoBlogData, VideoBlogPagination } from '@/apis/videoBlogs';
import YouTubeBlogCard from './YouTubeBlogCard';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, ChevronLeft, ChevronRight, Video, AlertCircle, Filter } from 'lucide-react';
import { toast } from 'sonner';

interface YouTubeBlogGridProps {
  initialPage?: number;
  itemsPerPage?: number;
  showSearch?: boolean;
  showFilters?: boolean;
  showPagination?: boolean;
  className?: string;
}

const YouTubeBlogGrid: React.FC<YouTubeBlogGridProps> = ({
  initialPage = 1,
  itemsPerPage = 12,
  showSearch = true,
  showFilters = true,
  showPagination = true,
  className = ""
}) => {
  const [blogs, setBlogs] = useState<VideoBlogData[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [pagination, setPagination] = useState<VideoBlogPagination>({
    currentPage: initialPage,
    totalPages: 0,
    totalItems: 0,
    itemsPerPage,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const fetchedCategories = await getVideoBlogCategories();
      setCategories(fetchedCategories);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  // Fetch blogs
  const fetchBlogs = async (page: number = 1, search?: string, category?: string) => {
    setIsLoading(true);
    try {
      const result = await getVideoBlogs(page, itemsPerPage, search, category);
      setBlogs(result.videoBlogs);
      setPagination(result.pagination);
    } catch (error) {
      console.error('Error fetching YouTube blogs:', error);
      toast.error('Failed to load VLOGS');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchCategories();
    fetchBlogs(initialPage);
  }, [initialPage, itemsPerPage]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const trimmedSearch = searchInput.trim();
    setSearchTerm(trimmedSearch);
    fetchBlogs(1, trimmedSearch || undefined, selectedCategory !== 'all' ? selectedCategory : undefined);
  };

  // Handle category change
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    fetchBlogs(1, searchTerm || undefined, category !== 'all' ? category : undefined);
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      fetchBlogs(newPage, searchTerm || undefined, selectedCategory !== 'all' ? selectedCategory : undefined);
      // Scroll to top of grid
      document.getElementById('youtube-blog-grid')?.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  // Clear filters
  const clearFilters = () => {
    setSearchInput('');
    setSearchTerm('');
    setSelectedCategory('all');
    fetchBlogs(1);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <div id="youtube-blog-grid" className={`w-full ${className}`}>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Video className="w-8 h-8 text-accent" />
          <h2 className="text-3xl font-bold text-gray-900">Vlogs</h2>
        </div>
        
        <p className="text-gray-600 mb-6">
          Watch our collection of YouTube videos showcasing Chinioti wooden art, craftsmanship techniques, and behind-the-scenes content.
        </p>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          {/* Search */}
          {showSearch && (
            <form onSubmit={handleSearch} className="flex gap-2 flex-1">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  type="text"
                  placeholder="Search VLOGS..."
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button type="submit" disabled={isLoading}>
                Search
              </Button>
            </form>
          )}

          {/* Category Filter */}
          {showFilters && (
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-500" />
              <Select value={selectedCategory} onValueChange={handleCategoryChange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Clear filters */}
          {(searchTerm || selectedCategory !== 'all') && (
            <Button variant="outline" onClick={clearFilters}>
              Clear Filters
            </Button>
          )}
        </div>

        {/* Filter results info */}
        {(searchTerm || selectedCategory !== 'all') && (
          <div className="text-sm text-gray-600 mb-4">
            {pagination.totalItems > 0 ? (
              <>
                Found {pagination.totalItems} video{pagination.totalItems !== 1 ? 's' : ''}
                {searchTerm && ` for "${searchTerm}"`}
                {selectedCategory !== 'all' && ` in ${selectedCategory}`}
              </>
            ) : (
              <>No videos found with current filters</>
            )}
          </div>
        )}
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
        </div>
      )}

      {/* Empty state */}
      {!isLoading && blogs.length === 0 && (
        <div className="text-center py-12">
          <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            {searchTerm || selectedCategory !== 'all' ? 'No videos found' : 'No VLOGS available'}
          </h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || selectedCategory !== 'all'
              ? 'Try adjusting your search terms or filters.'
              : 'Check back later for new video content.'
            }
          </p>
          {(searchTerm || selectedCategory !== 'all') && (
            <Button onClick={clearFilters} variant="outline">
              View All Videos
            </Button>
          )}
        </div>
      )}

      {/* Blog grid */}
      {!isLoading && blogs.length > 0 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        >
          {blogs.map((blog) => (
            <motion.div key={blog.id} variants={itemVariants}>
              <YouTubeBlogCard blog={blog} />
            </motion.div>
          ))}
        </motion.div>
      )}

      {/* Pagination */}
      {showPagination && !isLoading && pagination.totalPages > 1 && (
        <div className="mt-12 flex items-center justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pagination.currentPage - 1)}
            disabled={!pagination.hasPrevPage}
          >
            <ChevronLeft className="w-4 h-4" />
            Previous
          </Button>

          <div className="flex items-center gap-1">
            {/* Page numbers */}
            {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
              let pageNumber;
              if (pagination.totalPages <= 5) {
                pageNumber = i + 1;
              } else if (pagination.currentPage <= 3) {
                pageNumber = i + 1;
              } else if (pagination.currentPage >= pagination.totalPages - 2) {
                pageNumber = pagination.totalPages - 4 + i;
              } else {
                pageNumber = pagination.currentPage - 2 + i;
              }

              return (
                <Button
                  key={pageNumber}
                  variant={pageNumber === pagination.currentPage ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageChange(pageNumber)}
                  className="w-10"
                >
                  {pageNumber}
                </Button>
              );
            })}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pagination.currentPage + 1)}
            disabled={!pagination.hasNextPage}
          >
            Next
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      )}

      {/* Pagination info */}
      {showPagination && !isLoading && blogs.length > 0 && (
        <div className="mt-4 text-center text-sm text-gray-600">
          Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to{' '}
          {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{' '}
          {pagination.totalItems} video{pagination.totalItems !== 1 ? 's' : ''}
        </div>
      )}
    </div>
  );
};

export default YouTubeBlogGrid;
