/**
 * Terms of Service Acceptance Utilities
 * 
 * This module handles the storage and retrieval of terms acceptance data
 * in localStorage with proper error handling and type safety.
 */

export interface TermsAcceptanceData {
  accepted: boolean;
  timestamp: string;
  version: string;
  userAgent?: string;
}

// Current version of terms - increment when terms are updated
export const CURRENT_TERMS_VERSION = "1.0.0";

// localStorage key for terms acceptance
const TERMS_ACCEPTANCE_KEY = "chinioti_terms_acceptance";

/**
 * Check if user has accepted the current version of terms
 */
export function hasAcceptedTerms(): boolean {
  try {
    const stored = localStorage.getItem(TERMS_ACCEPTANCE_KEY);
    if (!stored) return false;

    const data: TermsAcceptanceData = JSON.parse(stored);
    
    // Check if user accepted the current version
    return data.accepted && data.version === CURRENT_TERMS_VERSION;
  } catch (error) {
    console.error("Error checking terms acceptance:", error);
    return false;
  }
}

/**
 * Record user's acceptance of terms
 */
export function acceptTerms(): void {
  try {
    const acceptanceData: TermsAcceptanceData = {
      accepted: true,
      timestamp: new Date().toISOString(),
      version: CURRENT_TERMS_VERSION,
      userAgent: typeof window !== "undefined" ? window.navigator.userAgent : undefined,
    };

    localStorage.setItem(TERMS_ACCEPTANCE_KEY, JSON.stringify(acceptanceData));
  } catch (error) {
    console.error("Error storing terms acceptance:", error);
  }
}

/**
 * Get terms acceptance data
 */
export function getTermsAcceptanceData(): TermsAcceptanceData | null {
  try {
    const stored = localStorage.getItem(TERMS_ACCEPTANCE_KEY);
    if (!stored) return null;

    return JSON.parse(stored);
  } catch (error) {
    console.error("Error retrieving terms acceptance data:", error);
    return null;
  }
}

/**
 * Clear terms acceptance (for testing or when terms are updated)
 */
export function clearTermsAcceptance(): void {
  try {
    localStorage.removeItem(TERMS_ACCEPTANCE_KEY);
  } catch (error) {
    console.error("Error clearing terms acceptance:", error);
  }
}

/**
 * Check if we should show the terms modal
 * This function can be extended with additional logic like:
 * - Checking if user is on certain pages
 * - Checking user authentication status
 * - Checking if enough time has passed since last check
 */
export function shouldShowTermsModal(): boolean {
  // Don't show on server side
  if (typeof window === "undefined") return false;
  
  // Don't show if already accepted current version
  if (hasAcceptedTerms()) return false;
  
  // Add any additional conditions here
  return true;
}
