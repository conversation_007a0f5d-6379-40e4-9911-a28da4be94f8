'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Cart, CartContextType, CartItem } from '@/types/cart';
import { ProductData } from '@/types';
import { toast } from 'sonner';
import { convertCurrency } from '@/lib/utils';
import { CurrencyCode } from '@/constants/helpers';

// Initial cart state
const initialCart: Cart = {
  items: [],
  totalItems: 0,
  subtotal: 0,
  discount: 0,
  total: 0,
};

// Create context
const CartContext = createContext<CartContextType | undefined>(undefined);

// Provider component
export const CartProvider = ({ children }: { children: ReactNode }) => {
  const [cart, setCart] = useState<Cart>(initialCart);

  // Load cart from localStorage on initial render
  useEffect(() => {
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      try {
        setCart(JSON.parse(savedCart));
      } catch (error) {
        console.error('Failed to parse cart from localStorage:', error);
        localStorage.removeItem('cart');
      }
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('cart', JSON.stringify(cart));
  }, [cart]);

  // Calculate cart totals with currency support
  const calculateTotals = (items: CartItem[], currency: CurrencyCode = 'USD'): Cart => {
    const totalItems = items.reduce((total, item) => total + item.quantity, 0);

    const subtotal = items.reduce(
      (total, item) => {
        // Convert price to selected currency
        const convertedPrice = convertCurrency(item.product.price, 'USD', currency);
        return total + convertedPrice * item.quantity;
      },
      0
    );

    const discount = items.reduce(
      (total, item) => {
        if (item.product.discount) {
          const convertedPrice = convertCurrency(item.product.price, 'USD', currency);
          const discountAmount = (convertedPrice * parseInt(item.product.discount) / 100) * item.quantity;
          return total + discountAmount;
        }
        return total;
      },
      0
    );

    return {
      items,
      totalItems,
      subtotal,
      discount,
      total: subtotal - discount,
    };
  };

  // Add product to cart
  const addToCart = (product: ProductData, quantity: number = 1) => {
    setCart((prevCart) => {
      const existingItemIndex = prevCart.items.findIndex(
        (item) => item.product.id === product.id
      );

      let updatedItems: CartItem[];

      if (existingItemIndex >= 0) {
        // Update quantity if product already in cart
        updatedItems = [...prevCart.items];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + quantity,
        };
        toast.success(`Updated ${product.title} quantity in cart`);
      } else {
        // Add new product to cart
        updatedItems = [...prevCart.items, { product, quantity }];
        toast.success(`Added ${product.title} to cart`);
      }

      return calculateTotals(updatedItems);
    });
  };

  // Remove product from cart
  const removeFromCart = (productId: string) => {
    setCart((prevCart) => {
      const updatedItems = prevCart.items.filter(
        (item) => item.product.id !== productId
      );
      
      const removedItem = prevCart.items.find(item => item.product.id === productId);
      if (removedItem) {
        toast.info(`Removed ${removedItem.product.title} from cart`);
      }
      
      return calculateTotals(updatedItems);
    });
  };

  // Update product quantity
  const updateQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId);
      return;
    }

    setCart((prevCart) => {
      const updatedItems = prevCart.items.map((item) =>
        item.product.id === productId ? { ...item, quantity } : item
      );
      
      return calculateTotals(updatedItems);
    });
  };

  // Clear cart
  const clearCart = () => {
    setCart(initialCart);
    toast.info('Cart cleared');
  };

  // Check if product is in cart
  const isInCart = (productId: string): boolean => {
    return cart.items.some((item) => item.product.id === productId);
  };

  return (
    <CartContext.Provider
      value={{
        cart,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart,
        isInCart,
      }}
    >
      {children}
    </CartContext.Provider>
  );
};

// Custom hook to use cart context
export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
