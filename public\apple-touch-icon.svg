<?xml version="1.0" encoding="UTF-8"?>
<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with rounded corners (Apple style) -->
  <rect width="180" height="180" rx="45" fill="#8B4513"/>

  <!-- Subtle Wood Grain Background Pattern -->
  <g opacity="0.1">
    <path d="M45 45C45 45 55 50 55 67.5C55 85 45 90 45 107.5C45 125 55 130 55 147.5" stroke="#D2B48C" stroke-width="3"/>
    <path d="M67.5 45C67.5 45 77.5 50 77.5 67.5C77.5 85 67.5 90 67.5 107.5C67.5 125 77.5 130 77.5 147.5" stroke="#D2B48C" stroke-width="3"/>
    <path d="M90 45C90 45 100 50 100 67.5C100 85 90 90 90 107.5C90 125 100 130 100 147.5" stroke="#D2B48C" stroke-width="3"/>
    <path d="M112.5 45C112.5 45 122.5 50 122.5 67.5C122.5 85 112.5 90 112.5 107.5C112.5 125 122.5 130 122.5 147.5" stroke="#D2B48C" stroke-width="3"/>
    <path d="M135 45C135 45 145 50 145 67.5C145 85 135 90 135 107.5C135 125 145 130 145 147.5" stroke="#D2B48C" stroke-width="3"/>
  </g>

  <!-- Inner Wooden Frame -->
  <rect x="30" y="30" width="120" height="120" rx="30" fill="#D2B48C"/>

  <!-- Decorative Carving Patterns -->
  <rect x="45" y="45" width="90" height="90" rx="22.5" stroke="#8B4513" stroke-width="4" fill="none"/>
  <rect x="60" y="60" width="60" height="60" rx="15" stroke="#8B4513" stroke-width="4" fill="none"/>

  <!-- Central Design Element -->
  <path d="M90 50L115 90L90 130L65 90L90 50Z" fill="#8B4513"/>

  <!-- Center Highlight -->
  <rect x="80" y="80" width="20" height="20" rx="5" fill="#D2B48C" stroke="#8B4513" stroke-width="2"/>

  <!-- Subtle Highlights -->
  <circle cx="90" cy="90" r="45" fill="white" opacity="0.05"/>
  <circle cx="90" cy="90" r="30" fill="white" opacity="0.05"/>

  <!-- "CWA" Initials -->
  <text x="90" y="95" font-family="serif" font-weight="bold" font-size="20" fill="#8B4513" text-anchor="middle">CWA</text>

  <!-- Subtle Glow Effect -->
  <circle cx="90" cy="90" r="75" fill="url(#paint0_radial)" opacity="0.2"/>

  <!-- Define the radial gradient for the glow effect -->
  <defs>
    <radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(90 90) rotate(90) scale(75)">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
    </radialGradient>
  </defs>
</svg>
