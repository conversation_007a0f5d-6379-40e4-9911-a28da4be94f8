"use client";
import { secondaryFont } from "@/constants/fonts";
import React from "react";
import { Button } from "./ui/button";
import { toast } from "sonner";

const InfoCard: React.FC<InfoCardProps> = ({
  title,
  desc,
  button_text,
  link = "",
}) => {
  function clickHandler() {
    toast.info(`${title}, ${desc}, ${button_text}, ${link}`);
  }
  return (
    <div className="w-full flex flex-col gap-2 items-start py-4">
      <h1 className={secondaryFont + "text-3xl"}>{title}</h1>
      <p className="leading-5 tracking-wide line-clamp-2 text-md text-secondary">
        {desc}
      </p>
      <Button onClick={clickHandler} color="light">
        {button_text}
        {" >"}
      </Button>
    </div>
  );
};

export default InfoCard;
