import apiClient from "@/lib/api/apiClient";

/**
 * Contact form data interface
 */
export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
}

/**
 * Submit contact form data to the backend
 * @param formData - The contact form data
 * @returns Promise with the response data or error
 */
export const submitContactForm = async (
  formData: ContactFormData
): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await apiClient.post("/contact", formData);

    if (response.data && response.data.status === "success") {
      return {
        success: true,
        message:
          response.data.message || "Your message has been sent successfully!",
      };
    } else {
      return {
        success: false,
        message:
          response.data.message ||
          "An error occurred while sending your message. Please try again.",
      };
    }
  } catch (error: unknown) {
    console.error("Error submitting contact form:", error);

    // Extract error message from response if available
    const errorMessage =
      (error as any).response?.data?.message ||
      "An error occurred while sending your message. Please try again later.";

    return {
      success: false,
      message: errorMessage,
    };
  }
};
