import apiClient from "@/lib/api/apiClient";
import { Order, OrderStatus } from "@/types/order";
import { CartItem } from "@/types/cart";

// Map frontend order to backend format
const mapFrontendOrderToBackend = (
  order: Omit<Order, "id" | "createdAt">,
  userId: string
) => {
  // Validate order items
  if (!order.items || !Array.isArray(order.items)) {
    console.error("Invalid order items:", order.items);
    throw new Error("Order items must be an array");
  }

  // Filter out any invalid items
  const validItems = order.items.filter(
    (item) => item && item.product && item.product.id
  );

  if (validItems.length === 0) {
    console.error("No valid items in order");
    throw new Error("Order must contain at least one valid item");
  }

  // Create items array in the format expected by backend
  const items = validItems.map((item) => {
    // Get the best available image
    let imageUrl = "https://via.placeholder.com/150"; // Default placeholder
    if (item.product.images && item.product.images.length > 0) {
      imageUrl = item.product.images[0];
    } else if (item.product.image) {
      imageUrl = item.product.image;
    }

    return {
      productId: item.product.id,
      name: item.product.title || item.product.name || "Product",
      price: item.product.price || 0,
      quantity: item.quantity,
      image: imageUrl,
    };
  });

  // Create shipping address object
  const shippingAddress = {
    name: `${order.customer.firstName} ${order.customer.lastName}`,
    address: order.shipping.address,
    city: order.shipping.city,
    postalCode: order.shipping.postalCode || "",
    country: "Pakistan", // Default country, can be made configurable
    phone: order.customer.phone,
  };

  return {
    user: userId,
    items: items,
    total: order.total,
    status: order.status,
    shippingAddress: shippingAddress,
    paymentMethod: order.paymentMethod === "cash" ? "cash_on_delivery" :
                   order.paymentMethod === "bank" ? "bank_transfer" : "cash_on_delivery",
    notes: "", // Can be added to frontend form if needed
    // Additional metadata that might be useful for the backend
    metadata: {
      customer: order.customer,
      shipping: order.shipping,
      paymentMethod: order.paymentMethod,
      subtotal: order.subtotal,
      discount: order.discount,
    },
  };
};

// Map backend order to frontend format
const mapBackendOrderToFrontend = (backendOrder: any): Order => {
  if (!backendOrder) {
    console.error("Received null or undefined backendOrder");
    return {
      id: "",
      items: [],
      total: 0,
      subtotal: 0,
      discount: 0,
      customer: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
      },
      shipping: {
        address: "",
        city: "",
        postalCode: "",
      },
      paymentMethod: "cash",
      status: "pending",
      createdAt: new Date().toISOString(),
    };
  }

  // Extract metadata (or use defaults if not available)
  const metadata = backendOrder.metadata || {};

  // Log the order data for debugging
  console.log("Backend order data:", backendOrder);
  console.log("Metadata:", metadata);

  // For orders created before metadata was added, try to reconstruct from products
  let items = [];

  // First try to get items directly from the order (new format)
  if (backendOrder.items && Array.isArray(backendOrder.items)) {
    items = backendOrder.items.map((item: any) => ({
      product: {
        id: item.productId || "",
        title: item.name || "Product",
        price: item.price || 0,
        images: item.image ? [item.image] : [],
        image: item.image || "",      },
      quantity: item.quantity || 1,
    }));
  }
  // If no items found, try to get items from metadata
  else if (metadata.items && Array.isArray(metadata.items)) {
    // Validate each item has a product property
    items = metadata.items
      .filter((item:any) => item && item.product)
      .map((item:any) => ({
        product: {
          id: item.product.id || item.productId || "",
          title: item.product.title || "Product",
          price: item.product.price || 0,
          images: item.product.images || [],
          image: item.product.image || "",
        },
        quantity: item.quantity || 1,
      }));
  }
  // If no items found in metadata, try to reconstruct from products (legacy)
  else if (
    backendOrder.products &&
    Array.isArray(backendOrder.products)
  ) {
    // If we have populated products from the backend
    backendOrder.products.forEach((product: any) => {
      if (product) {
        items.push({
          product: {
            id: product._id || "",
            title: product.name || "Product",
            price: product.price || 0,
            images: product.images || (product.image ? [product.image] : []),
            image: product.image || "",
          },
          quantity: 1, // Default quantity since we don't know the actual quantity
        });
      }
    });
  }

  return {
    id: backendOrder._id || "",
    items: items,
    total: backendOrder.total || 0,
    subtotal: metadata.subtotal || backendOrder.total || 0,
    discount: metadata.discount || 0,
    customer: metadata.customer || {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
    },
    shipping: metadata.shipping || {
      address: "",
      city: "",
      postalCode: "",
    },
    paymentMethod: metadata.paymentMethod || "cash",
    status: (backendOrder.status as OrderStatus) || "pending",
    createdAt: backendOrder.createdAt || new Date().toISOString(),
    updatedAt: backendOrder.updatedAt,
  };
};

/**
 * Create a new order
 */
export const createOrder = async (
  orderData: Omit<Order, "id" | "createdAt">,
  userId: string
): Promise<Order> => {
  try {
    const backendOrderData = mapFrontendOrderToBackend(orderData, userId);

    const response = await apiClient.post("/orders", backendOrderData);

    if (response.data && response.data.status === "success") {
      return mapBackendOrderToFrontend(response.data.data.order);
    } else {
      throw new Error("Failed to create order");
    }
  } catch (error) {
    console.error("Error creating order:", error);
    throw error;
  }
};

/**
 * Get all orders for the current user
 */
export const getUserOrders = async (userId: string): Promise<Order[]> => {
  try {
    // Assuming the backend has a way to filter orders by user
    // This might need to be adjusted based on the actual backend implementation
    const response = await apiClient.get(`/orders?user=${userId}`);

    if (response.data && response.data.status === "success") {
      return response.data.data.orders.map(mapBackendOrderToFrontend);
    } else {
      return [];
    }
  } catch (error) {
    console.error("Error fetching user orders:", error);
    return [];
  }
};

/**
 * Get a single order by ID
 */
export const getOrderById = async (orderId: string): Promise<Order | null> => {
  try {
    const response = await apiClient.get(`/orders/${orderId}`);

    if (response.data && response.data.status === "success") {
      return mapBackendOrderToFrontend(response.data.data.order);
    } else {
      return null;
    }
  } catch (error) {
    console.error(`Error fetching order with ID ${orderId}:`, error);
    return null;
  }
};

/**
 * Update order status
 */
export const updateOrderStatus = async (
  orderId: string,
  status: OrderStatus
): Promise<Order | null> => {
  try {
    const response = await apiClient.patch(`/orders/${orderId}`, { status });

    if (response.data && response.data.status === "success") {
      return mapBackendOrderToFrontend(response.data.data.order);
    } else {
      return null;
    }
  } catch (error) {
    console.error(`Error updating order status for ID ${orderId}:`, error);
    return null;
  }
};

/**
 * Cancel an order (update status to cancelled)
 */
export const cancelOrder = async (orderId: string): Promise<Order | null> => {
  return updateOrderStatus(orderId, "cancelled");
};
