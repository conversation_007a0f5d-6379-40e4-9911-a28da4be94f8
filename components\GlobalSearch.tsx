"use client";

import React, { useState, useEffect } from "react";
import <PERSON><PERSON>ield from "./SearchField";
import { getProducts } from "@/apis/products";
import { useRouter } from "next/navigation";

const GlobalSearch: React.FC = () => {
  const router = useRouter();
  const [products, setProducts] = useState<ProductData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch products for search suggestions
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const allProducts = await getProducts();
        setProducts(allProducts);
      } catch (error) {
        console.error("Error fetching products for search:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Handle product selection
  const handleSelectProduct = (product: ProductData) => {
    router.push(`/products/${product.id}`);
  };

  return (
    <div className="relative z-20">
      <SearchField
        placeholder="Search products..."
        debounceTime={300}
        products={products}
        showSuggestions={true}
        maxSuggestions={5}
        onSelectSuggestion={handleSelectProduct}
      />
    </div>
  );
};

export default GlobalSearch;
