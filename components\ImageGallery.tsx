"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import Image from "next/image";
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, X, Maximize2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { IMAGE_PLACEHOLDER_URL } from "@/constants/helpers";
import ImageZoomModal from "./ImageZoomModal";
import ImageLoadingState from "./ImageLoadingState";
import { useBreakpoints } from "@/hooks/useMediaQuery";

interface ImageGalleryProps {
  images: string[];
  alt: string;
  className?: string;
  showThumbnails?: boolean;
  showZoom?: boolean;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  priority?: boolean;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  alt,
  className = "",
  showThumbnails = true,
  showZoom = true,
  autoPlay = false,
  autoPlayInterval = 5000,
  priority = false,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomPosition, setZoomPosition] = useState({ x: 50, y: 50 });
  const [isLoading, setIsLoading] = useState(true);
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());
  const [loadedImages, setLoadedImages] = useState<Set<number>>(new Set());
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const [showZoomModal, setShowZoomModal] = useState(false);
  const [announceText, setAnnounceText] = useState("");
  const galleryRef = useRef<HTMLDivElement>(null);
  const thumbnailsRef = useRef<HTMLDivElement>(null);

  // Responsive breakpoints
  const { isMobile, isTablet, isTouchDevice } = useBreakpoints();

  // Ensure we have valid images
  const validImages = images.length > 0 ? images : [IMAGE_PLACEHOLDER_URL];
  const currentImage = validImages[currentIndex] || IMAGE_PLACEHOLDER_URL;

  // Auto-play functionality
  useEffect(() => {
    if (!autoPlay || validImages.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % validImages.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [autoPlay, autoPlayInterval, validImages.length]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!galleryRef.current?.contains(document.activeElement)) return;

      switch (e.key) {
        case "ArrowLeft":
          e.preventDefault();
          goToPrevious();
          break;
        case "ArrowRight":
          e.preventDefault();
          goToNext();
          break;
        case "Escape":
          e.preventDefault();
          setIsZoomed(false);
          break;
        case "Enter":
        case " ":
          e.preventDefault();
          toggleZoom();
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  const goToNext = useCallback(() => {
    const nextIndex = (currentIndex + 1) % validImages.length;
    setCurrentIndex(nextIndex);
    setAnnounceText(`Image ${nextIndex + 1} of ${validImages.length}`);
  }, [currentIndex, validImages.length]);

  const goToPrevious = useCallback(() => {
    const prevIndex = (currentIndex - 1 + validImages.length) % validImages.length;
    setCurrentIndex(prevIndex);
    setAnnounceText(`Image ${prevIndex + 1} of ${validImages.length}`);
  }, [currentIndex, validImages.length]);

  const goToSlide = useCallback((index: number) => {
    setCurrentIndex(index);
    setAnnounceText(`Image ${index + 1} of ${validImages.length}`);
  }, [validImages.length]);

  const toggleZoom = useCallback(() => {
    // On mobile or touch devices, always use the modal for better UX
    if (isMobile || isTouchDevice) {
      setShowZoomModal(true);
    } else {
      setIsZoomed(!isZoomed);
    }
  }, [isZoomed, isMobile, isTouchDevice]);

  const openZoomModal = useCallback(() => {
    setShowZoomModal(true);
  }, []);

  // Enhanced touch handlers for swipe gestures
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const minSwipeDistance = isMobile ? 30 : 50; // Shorter distance on mobile
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe && validImages.length > 1) {
      goToNext();
    } else if (isRightSwipe && validImages.length > 1) {
      goToPrevious();
    }
  };

  // Mouse move handler for zoom positioning
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isZoomed) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    setZoomPosition({ x, y });
  };

  // Image load handler
  const handleImageLoad = (index: number) => {
    setLoadedImages((prev) => new Set([...prev, index]));
    setImageErrors((prev) => {
      const newErrors = new Set(prev);
      newErrors.delete(index);
      return newErrors;
    });
    if (index === currentIndex) {
      setIsLoading(false);
    }
  };

  // Image error handler
  const handleImageError = (index: number) => {
    setImageErrors((prev) => new Set([...prev, index]));
    if (index === currentIndex) {
      setIsLoading(false);
    }
  };

  // Scroll thumbnails to show current image
  useEffect(() => {
    if (!showThumbnails || !thumbnailsRef.current) return;

    const thumbnail = thumbnailsRef.current.children[currentIndex] as HTMLElement;
    if (thumbnail) {
      thumbnail.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
        inline: "center",
      });
    }
  }, [currentIndex, showThumbnails]);

  return (
    <div
      ref={galleryRef}
      className={`relative w-full ${className}`}
      role="region"
      aria-label={`Product image gallery with ${validImages.length} images`}
      aria-describedby="gallery-instructions"
      tabIndex={0}
    >
      {/* Main image container */}
      <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-100 group">
        {/* Enhanced loading and error states */}
        <ImageLoadingState
          isLoading={isLoading && !loadedImages.has(currentIndex)}
          hasError={imageErrors.has(currentIndex)}
          size={isMobile ? "sm" : "md"}
        />

        {/* Main image */}
        <div
          className={`relative w-full h-full transition-transform duration-300 ${
            showZoom && !isMobile && !isTouchDevice
              ? `cursor-${isZoomed ? "zoom-out" : "zoom-in"} ${isZoomed ? "scale-150" : "scale-100"}`
              : "cursor-default"
          }`}
          onClick={showZoom ? toggleZoom : undefined}
          onMouseMove={!isTouchDevice ? handleMouseMove : undefined}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          style={
            isZoomed && !isMobile && !isTouchDevice
              ? {
                  transformOrigin: `${zoomPosition.x}% ${zoomPosition.y}%`,
                }
              : undefined
          }
        >
          <Image
            src={imageErrors.has(currentIndex) ? IMAGE_PLACEHOLDER_URL : currentImage}
            alt={`${alt} - Image ${currentIndex + 1} of ${validImages.length}. ${
              imageErrors.has(currentIndex) ? "Fallback image displayed due to loading error." : ""
            }`}
            fill
            className="object-cover transition-opacity duration-300 no-drag"
            priority={priority && currentIndex === 0}
            sizes={
              isMobile
                ? "100vw"
                : isTablet
                ? "(max-width: 1023px) 100vw, 50vw"
                : "(max-width: 1439px) 50vw, 40vw"
            }
            onLoad={() => handleImageLoad(currentIndex)}
            onError={() => handleImageError(currentIndex)}
          />
        </div>

        {/* Navigation arrows - responsive sizing */}
        {validImages.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="icon"
              className={`absolute left-1 sm:left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white/90 transition-opacity duration-200 touch-target ${
                isTouchDevice ? "opacity-100" : "opacity-0 group-hover:opacity-100"
              } ${isMobile ? "w-10 h-10" : "w-12 h-12"}`}
              onClick={goToPrevious}
              aria-label="Previous image"
            >
              <ChevronLeft className={isMobile ? "w-4 h-4" : "w-5 h-5"} />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className={`absolute right-1 sm:right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white/90 transition-opacity duration-200 touch-target ${
                isTouchDevice ? "opacity-100" : "opacity-0 group-hover:opacity-100"
              } ${isMobile ? "w-10 h-10" : "w-12 h-12"}`}
              onClick={goToNext}
              aria-label="Next image"
            >
              <ChevronRight className={isMobile ? "w-4 h-4" : "w-5 h-5"} />
            </Button>
          </>
        )}

        {/* Zoom controls - responsive */}
        {showZoom && (
          <div className={`absolute top-1 sm:top-2 right-1 sm:right-2 flex gap-1 transition-opacity duration-200 ${
            isTouchDevice ? "opacity-100" : "opacity-0 group-hover:opacity-100"
          }`}>
            {!isMobile && !isTouchDevice && (
              <Button
                variant="ghost"
                size="icon"
                className="bg-white/80 hover:bg-white/90 w-8 h-8"
                onClick={toggleZoom}
                aria-label={isZoomed ? "Zoom out" : "Zoom in"}
              >
                {isZoomed ? <ZoomOut className="w-4 h-4" /> : <ZoomIn className="w-4 h-4" />}
              </Button>
            )}
            <Button
              variant="ghost"
              size="icon"
              className={`bg-white/80 hover:bg-white/90 ${isMobile ? "w-10 h-10" : "w-8 h-8"}`}
              onClick={openZoomModal}
              aria-label="Open full screen zoom"
            >
              <Maximize2 className={isMobile ? "w-5 h-5" : "w-4 h-4"} />
            </Button>
          </div>
        )}

        {/* Image indicators - responsive sizing */}
        {validImages.length > 1 && (
          <div className={`absolute bottom-1 sm:bottom-2 left-1/2 -translate-x-1/2 flex gap-1 ${
            isMobile ? "gap-2" : "gap-1"
          }`}>
            {validImages.map((_, index) => (
              <button
                key={index}
                className={`rounded-full transition-all duration-200 touch-target ${
                  isMobile ? "w-3 h-3" : "w-2 h-2"
                } ${
                  index === currentIndex ? "bg-white" : "bg-white/50"
                }`}
                onClick={() => goToSlide(index)}
                aria-label={`Go to image ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Thumbnails - responsive sizing */}
      {showThumbnails && validImages.length > 1 && (
        <div
          ref={thumbnailsRef}
          className={`flex gap-2 mt-3 sm:mt-4 overflow-x-auto pb-2 scrollbar-hide ${
            isMobile ? "gap-1.5" : "gap-2"
          }`}
          role="tablist"
          aria-label="Image thumbnails"
        >
          {validImages.map((image, index) => (
            <button
              key={index}
              role="tab"
              aria-selected={index === currentIndex}
              aria-label={`View image ${index + 1}`}
              className={`relative flex-shrink-0 rounded-md overflow-hidden border-2 transition-all duration-200 touch-target ${
                isMobile ? "w-16 h-16" : "w-20 h-20"
              } ${
                index === currentIndex
                  ? "border-accent ring-2 ring-accent/20"
                  : "border-transparent hover:border-gray-300"
              }`}
              onClick={() => goToSlide(index)}
            >
              <Image
                src={image}
                alt={`${alt} thumbnail ${index + 1}`}
                fill
                className="object-cover"
                sizes={isMobile ? "64px" : "80px"}
                loading="lazy"
              />
              {!loadedImages.has(index) && (
                <div className="absolute inset-0 bg-gray-200 animate-pulse" />
              )}
            </button>
          ))}
        </div>
      )}

      {/* Enhanced accessibility features */}
      <div id="gallery-instructions" className="sr-only">
        Image gallery with {validImages.length} images. Use arrow keys to navigate between images.
        Press Enter or Space to zoom. Press Escape to exit zoom.
        Current image: {currentIndex + 1} of {validImages.length}.
      </div>

      {/* Live region for announcements */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        {announceText}
      </div>

      {/* Skip link for keyboard users */}
      <a
        href="#gallery-end"
        className="sr-only focus:not-sr-only focus:absolute focus:top-2 focus:left-2 bg-white px-2 py-1 rounded z-50"
      >
        Skip image gallery
      </a>

      <div id="gallery-end" className="sr-only">End of image gallery</div>

      {/* Zoom Modal */}
      <ImageZoomModal
        images={validImages}
        currentIndex={currentIndex}
        alt={alt}
        isOpen={showZoomModal}
        onClose={() => setShowZoomModal(false)}
        onIndexChange={setCurrentIndex}
      />
    </div>
  );
};

export default ImageGallery;
