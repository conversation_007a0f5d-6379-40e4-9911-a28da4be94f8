'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { CurrencyCode, CURRENCY_CONFIG, DEFAULT_CURRENCY } from '@/constants/helpers';

interface CurrencyContextType {
  selectedCurrency: CurrencyCode;
  setCurrency: (currency: CurrencyCode) => void;
  convertPrice: (price: number, fromCurrency?: CurrencyCode, toCurrency?: CurrencyCode) => number;
  formatPrice: (price: number, currency?: CurrencyCode) => string;
  getCurrencySymbol: (currency?: CurrencyCode) => string;
  getAllCurrencies: () => typeof CURRENCY_CONFIG;
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined);

interface CurrencyProviderProps {
  children: ReactNode;
}

export const CurrencyProvider: React.FC<CurrencyProviderProps> = ({ children }) => {
  const [selectedCurrency, setSelectedCurrency] = useState<CurrencyCode>(DEFAULT_CURRENCY);

  // Load saved currency preference from localStorage
  useEffect(() => {
    const savedCurrency = localStorage.getItem('selectedCurrency') as CurrencyCode;
    if (savedCurrency && CURRENCY_CONFIG[savedCurrency]) {
      setSelectedCurrency(savedCurrency);
    }
  }, []);

  // Save currency preference to localStorage
  const setCurrency = (currency: CurrencyCode) => {
    setSelectedCurrency(currency);
    localStorage.setItem('selectedCurrency', currency);
  };

  // Convert price between currencies
  const convertPrice = (
    price: number, 
    fromCurrency: CurrencyCode = 'USD', 
    toCurrency: CurrencyCode = selectedCurrency
  ): number => {
    if (fromCurrency === toCurrency) return price;
    
    // Convert to USD first (base currency)
    const priceInUSD = price / CURRENCY_CONFIG[fromCurrency].rate;
    
    // Convert from USD to target currency
    const convertedPrice = priceInUSD * CURRENCY_CONFIG[toCurrency].rate;
    
    return Math.round(convertedPrice);
  };

  // Format price with currency symbol
  const formatPrice = (price: number, currency: CurrencyCode = selectedCurrency): string => {
    const currencyInfo = CURRENCY_CONFIG[currency];
    const convertedPrice = convertPrice(price, 'USD', currency);
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(convertedPrice);
  };

  // Get currency symbol
  const getCurrencySymbol = (currency: CurrencyCode = selectedCurrency): string => {
    return CURRENCY_CONFIG[currency].symbol;
  };

  // Get all available currencies
  const getAllCurrencies = () => {
    return CURRENCY_CONFIG;
  };

  const value: CurrencyContextType = {
    selectedCurrency,
    setCurrency,
    convertPrice,
    formatPrice,
    getCurrencySymbol,
    getAllCurrencies,
  };

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
};

// Custom hook to use currency context
export const useCurrency = (): CurrencyContextType => {
  const context = useContext(CurrencyContext);
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
};
