"use client";
import Hero from "@/sections/Hero";
import ProductsSection from "@/sections/ProductsSection";
import { motion } from "framer-motion";
import OrganizationJsonLd from "@/components/SEO/OrganizationJsonLd";
import LocalBusinessJsonLd from "@/components/SEO/LocalBusinessJsonLd";
import FAQSchema, { chiniotFurnitureFAQs } from "@/components/SEO/FAQSchema";
import BreadcrumbJsonLd from "@/components/SEO/BreadcrumbJsonLd";
import Script from "next/script";
import { siteConfig, getUrl } from "@/config/site";
import { metadata } from "./metadata";

const Home = () => {
  // Base URL for the site
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || getUrl();

  // Website schema for home page
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "@id": `${baseUrl}/#website`,
    name: siteConfig.name,
    alternateName: siteConfig.shortName,
    description: siteConfig.description,
    url: baseUrl,
    inLanguage: "en-US",
    publisher: {
      "@id": `${baseUrl}/#organization`
    },
    potentialAction: {
      "@type": "SearchAction",
      target: {
        "@type": "EntryPoint",
        urlTemplate: `${baseUrl}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    about: {
      "@type": "Thing",
      name: "Chiniot Furniture",
      description: "Traditional handcrafted wooden furniture from Chiniot, Punjab, Pakistan",
      sameAs: [
        "https://en.wikipedia.org/wiki/Chiniot",
        "https://en.wikipedia.org/wiki/Furniture"
      ]
    },
    keywords: siteConfig.seo.keywords.join(", "),
    copyrightYear: new Date().getFullYear(),
    copyrightHolder: {
      "@id": `${baseUrl}/#organization`
    }
  };

  return (
    <>
      {/* Enhanced SEO Structured Data */}
      <OrganizationJsonLd url={baseUrl} />
      <LocalBusinessJsonLd url={baseUrl} />

      {/* Website Schema */}
      <Script
        id="website-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema)
        }}
      />

      {/* FAQ Schema for better search results */}
      <FAQSchema
        faqs={chiniotFurnitureFAQs.slice(0, 5)} // Show top 5 FAQs on home page
        title="Frequently Asked Questions about Chiniot Furniture"
        description="Common questions about our authentic handcrafted wooden furniture from Chiniot, Pakistan"
      />

      {/* Main Content with proper semantic structure */}
      <main>
        {/* Hero Section with H1 */}
        <Hero />

        {/* Featured Products Section */}
        <ProductsSection
          title="Featured Chiniot Furniture"
          subtitle="Discover our handpicked selection of exceptional Chinioti wooden furniture pieces, crafted by master artisans in Chiniot, Punjab, Pakistan using traditional techniques passed down through generations."
          filter="featured"
          headingLevel="h2"
          sectionId="featured-products"
        />

        {/* New Arrivals Section */}
        <ProductsSection
          title="New Arrivals from Chiniot"
          subtitle="The latest additions to our collection of premium Chinioti craftsmanship, featuring traditional woodworking techniques and modern designs that blend heritage with contemporary style."
          filter="new"
          limit={4}
          headingLevel="h2"
          sectionId="new-arrivals"
        />

        {/* Trending Products Section */}
        <ProductsSection
          title="Trending Handcrafted Furniture"
          subtitle="Our most popular Chinioti wooden furniture pieces that customers worldwide love for their authentic craftsmanship, premium quality, and timeless design from Pakistan's furniture capital."
          filter="trending"
          limit={8}
          headingLevel="h2"
          sectionId="trending-products"
        />

        {/* SEO Content Section */}
        <section className="py-16 px-4 max-w-7xl mx-auto">
          <div className="prose prose-lg max-w-none">
            <h2 className="text-3xl font-bold text-center mb-8">
              Authentic Chiniot Furniture - Traditional Pakistani Craftsmanship
            </h2>
            <div className="grid md:grid-cols-2 gap-8 text-gray-700">
              <div>
                <h3 className="text-xl font-semibold mb-4">Why Choose Chinioti Wooden Art?</h3>
                <p className="mb-4">
                  Chiniot, located in Punjab, Pakistan, is world-renowned for its exceptional wooden furniture craftsmanship.
                  For over 500 years, skilled artisans in this historic city have been creating masterpieces that blend
                  traditional techniques with contemporary designs.
                </p>
                <p>
                  Our furniture is handcrafted using premium Pakistani hardwoods including Sheesham, Teak, and Mango wood,
                  ensuring durability and timeless beauty that lasts for generations.
                </p>
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-4">Our Specialties</h3>
                <ul className="list-disc list-inside space-y-2">
                  <li>Handcrafted wooden beds with intricate carvings</li>
                  <li>Traditional dining sets and tables</li>
                  <li>Custom wardrobes and storage solutions</li>
                  <li>Decorative chairs and seating furniture</li>
                  <li>Bespoke furniture design services</li>
                  <li>International shipping worldwide</li>
                </ul>
              </div>
            </div>
          </div>
        </section>
      </main>
    </>
  );
};

export default Home;
