"use client";
import Image from "next/image";
import React from "react";
import { motion } from "framer-motion";
import Link from "next/link";

const CategoryCard: React.FC<Category> = ({ id, name, image }) => {
  return (
    <Link href={`/products?category=${name.toLowerCase()}`}>
      {/* <motion.div
        className="flex justify-center items-center flex-col cursor-pointer"
        whileHover={{ y: -5 }}
        transition={{ type: "spring", stiffness: 300 }}
      > */}
        <motion.div
          className="relative overflow-hidden rounded-lg  "
          whileHover={{ scale: 1.03 }}
          transition={{ type: "spring", stiffness: 400, damping: 10 }}
        >
          <motion.div
            className="absolute inset-0 bg-accent/5 opacity-0"
            whileHover={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          />

          <Image
            src={image[0]}
            alt={name}
            width={240}
            height={270}
            className="relative rounded-lg z-10 transition-transform duration-300"
          />
        </motion.div>

        <motion.div
          className="flex flex-col justify-center items-center "
          whileHover={{ scale: 1.05 }}
        >
          <h1 className="text-md font-medium text-secondary">{name}</h1>
          <motion.span
            className=" w-0 bg-accent mt-1"
            whileHover={{ width: "100%" }}
            transition={{ duration: 0.3 }}
          />
        </motion.div>
      {/* </motion.div> */}
    </Link>
  );
};

export default CategoryCard;
