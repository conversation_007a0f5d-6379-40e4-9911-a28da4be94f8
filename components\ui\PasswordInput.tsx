"use client";
import React, { useState, forwardRef } from 'react';
import { motion } from 'framer-motion';
import { Eye, EyeOff } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface PasswordInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  className?: string;
  showToggle?: boolean;
  onToggleVisibility?: (isVisible: boolean) => void;
}

const PasswordInput = forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ className, showToggle = true, onToggleVisibility, ...props }, ref) => {
    const [isVisible, setIsVisible] = useState(false);

    const toggleVisibility = () => {
      const newVisibility = !isVisible;
      setIsVisible(newVisibility);
      onToggleVisibility?.(newVisibility);
    };

    return (
      <motion.div whileFocus={{ scale: 1.01 }} className="relative">
        <input
          ref={ref}
          type={isVisible ? 'text' : 'password'}
          className={cn(
            "w-full px-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all",
            showToggle && "pr-12", // Add padding for toggle button
            className
          )}
          {...props}
        />
        {showToggle && (
          <button
            type="button"
            onClick={toggleVisibility}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none focus:text-gray-700 transition-colors"
            aria-label={isVisible ? 'Hide password' : 'Show password'}
            tabIndex={-1}
          >
            {isVisible ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </button>
        )}
      </motion.div>
    );
  }
);

PasswordInput.displayName = 'PasswordInput';

export default PasswordInput;
