"use client";
import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { Check } from 'lucide-react';

const ResetPasswordSuccess = () => {
  const router = useRouter();
  
  // Auto-redirect to sign-in page after 5 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      router.push('/sign-in');
    }, 5000);
    
    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="bg-card rounded-lg shadow-lg p-8 w-full">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="text-center"
      >
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4"
        >
          <Check className="h-8 w-8 text-green-600" />
        </motion.div>
        
        <h2 className="text-2xl font-bold mb-2">Password Reset Successful</h2>
        
        <p className="text-muted-foreground mb-6">
          Your password has been reset successfully. You can now sign in with your new password.
        </p>
        
        <p className="text-sm text-muted-foreground mb-6">
          You will be redirected to the sign-in page in a few seconds.
        </p>
        
        <Link href="/sign-in">
          <Button variant="default">Sign In Now</Button>
        </Link>
      </motion.div>
    </div>
  );
};

export default ResetPasswordSuccess;
