import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { CurrencyCode, CURRENCY_CONFIG } from '@/constants/helpers';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Legacy function - kept for backward compatibility
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'PKR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

// Enhanced currency formatting function
export function formatCurrencyWithCode(
  amount: number,
  currency: CurrencyCode = 'USD',
  convertFromUSD: boolean = true
): string {
  const currencyInfo = CURRENCY_CONFIG[currency];

  // Convert from USD to target currency if needed
  const finalAmount = convertFromUSD
    ? Math.round(amount * currencyInfo.rate)
    : amount;

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(finalAmount);
}

// Convert price between currencies
export function convertCurrency(
  amount: number,
  fromCurrency: CurrencyCode = 'USD',
  toCurrency: CurrencyCode = 'USD'
): number {
  if (fromCurrency === toCurrency) return amount;

  // Convert to USD first (base currency)
  const amountInUSD = amount / CURRENCY_CONFIG[fromCurrency].rate;

  // Convert from USD to target currency
  const convertedAmount = amountInUSD * CURRENCY_CONFIG[toCurrency].rate;

  return Math.round(convertedAmount);
}

// Format price with both currencies for comparison
export function formatDualCurrency(amount: number): string {
  const usdAmount = formatCurrencyWithCode(amount, 'USD', false);
  const pkrAmount = formatCurrencyWithCode(amount, 'PKR', true);
  return `${usdAmount} / ${pkrAmount}`;
}
