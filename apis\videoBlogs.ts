import apiClient from '@/lib/api/apiClient';

// Types for video blog data
export interface VideoBlogData {
  id: string;
  title: string;
  description: string;
  // Legacy fields for file-based videos (optional for YouTube videos)
  videoFileId?: string;
  videoFilename?: string;
  mimetype?: string;
  fileSize?: number;
  duration?: number;
  thumbnailFileId?: string;
  thumbnailFilename?: string;
  // New YouTube-specific fields
  youtubeUrl?: string;
  youtubeVideoId?: string;
  category: string;
  tags: string[];
  isActive: boolean;
  views: number;
  videoUrl: string;
  thumbnailUrl: string;
  createdAt: string;
  updatedAt: string;
}

export interface VideoBlogPagination {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface VideoBlogResponse {
  status: string;
  data: {
    videoBlogs: VideoBlogData[];
    pagination: VideoBlogPagination;
  };
}

export interface SingleVideoBlogResponse {
  status: string;
  data: {
    videoBlog: VideoBlogData;
  };
}

// Note: Caching is handled by the backend and browser cache

/**
 * Map backend video blog data to frontend format
 */
const mapBackendVideoBlogToFrontend = (backendVideoBlog: any): VideoBlogData => {
  return {
    id: backendVideoBlog._id || backendVideoBlog.id,
    title: backendVideoBlog.title,
    description: backendVideoBlog.description || backendVideoBlog.title || '', // Fallback for legacy data
    videoFileId: backendVideoBlog.videoFileId,
    videoFilename: backendVideoBlog.videoFilename,
    mimetype: backendVideoBlog.mimetype,
    fileSize: backendVideoBlog.fileSize,
    duration: backendVideoBlog.duration,
    thumbnailFileId: backendVideoBlog.thumbnailFileId,
    thumbnailFilename: backendVideoBlog.thumbnailFilename,
    youtubeUrl: backendVideoBlog.youtubeUrl,
    youtubeVideoId: backendVideoBlog.youtubeVideoId,
    category: backendVideoBlog.category || 'General',
    tags: backendVideoBlog.tags || [],
    isActive: backendVideoBlog.isActive,
    views: backendVideoBlog.views,
    videoUrl: backendVideoBlog.videoUrl,
    thumbnailUrl: backendVideoBlog.thumbnailUrl || '',
    createdAt: backendVideoBlog.createdAt,
    updatedAt: backendVideoBlog.updatedAt,
  };
};

/**
 * Get all VLOGS with pagination and optional search
 */
export const getVideoBlogs = async (
  page: number = 1,
  limit: number = 12,
  search?: string,
  category?: string
): Promise<{ videoBlogs: VideoBlogData[]; pagination: VideoBlogPagination }> => {
  try {
    console.log("Fetching VLOGS from API");
    
    // Build query parameters
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (search) {
      params.append('search', search);
    }

    if (category && category !== 'all') {
      params.append('category', category);
    }

    const response = await apiClient.get(`/video-blogs?${params.toString()}`);

    console.log("Rsesponse:", response.data);

    if (response.data && response.data.status === "success") {
      const backendVideoBlogs = response.data.data.videoBlogs;
      const mappedVideoBlogs = backendVideoBlogs.map(mapBackendVideoBlogToFrontend);

      return {
        videoBlogs: mappedVideoBlogs,
        pagination: response.data.data.pagination
      };
    } else {
      console.error("API returned unexpected format:", response.data);
      return {
        videoBlogs: [],
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: limit,
          hasNextPage: false,
          hasPrevPage: false
        }
      };
    }
  } catch (error) {
    console.error("Error fetching VLOGS:", error);
    return {
      videoBlogs: [],
      pagination: {
        currentPage: 1,
        totalPages: 0,
        totalItems: 0,
        itemsPerPage: limit,
        hasNextPage: false,
        hasPrevPage: false
      }
    };
  }
};

/**
 * Get a single video blog by ID
 */
export const getVideoBlogById = async (
  id: string
): Promise<VideoBlogData | undefined> => {
  try {
    const response = await apiClient.get(`/video-blogs/${id}`);

    if (response.data && response.data.status === "success") {
      return mapBackendVideoBlogToFrontend(response.data.data.videoBlog);
    } else {
      console.error("API returned unexpected format:", response.data);
      return undefined;
    }
  } catch (error) {
    console.error(`Error fetching video blog with ID ${id}:`, error);
    return undefined;
  }
};



/**
 * Create a new video blog
 */
export const createVideoBlog = async (
  videoBlogData: {
    title: string;
    description: string;
    videoFileId?: string;
    videoFilename?: string;
    mimetype?: string;
    fileSize?: number;
    duration?: number;
    thumbnailFileId?: string;
    thumbnailFilename?: string;
    videoUrl: string;
    thumbnailUrl: string;
    youtubeUrl?: string;
    youtubeVideoId?: string;
    category?: string;
    tags?: string[];
  }
): Promise<VideoBlogData | null> => {
  try {
    const response = await apiClient.post('/video-blogs', videoBlogData, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data && response.data.status === "success") {
      return mapBackendVideoBlogToFrontend(response.data.data.videoBlog);
    } else {
      console.error("API returned unexpected format:", response.data);
      return null;
    }
  } catch (error) {
    console.error("Error creating video blog:", error);
    throw error;
  }
};

/**
 * Update a video blog
 */
export const updateVideoBlog = async (
  id: string,
  updates: {
    title?: string;
    description?: string;
    isActive?: boolean;
    videoUrl?: string;
    thumbnailUrl?: string;
    duration?: number;
    videoFileId?: string;
    videoFilename?: string;
    mimetype?: string;
    fileSize?: number;
    thumbnailFileId?: string;
    thumbnailFilename?: string;
    youtubeUrl?: string;
    youtubeVideoId?: string;
    category?: string;
    tags?: string[];
  }
): Promise<VideoBlogData | null> => {
  try {
    const response = await apiClient.patch(`/video-blogs/${id}`, updates);

    if (response.data && response.data.status === "success") {
      return mapBackendVideoBlogToFrontend(response.data.data.videoBlog);
    } else {
      console.error("API returned unexpected format:", response.data);
      return null;
    }
  } catch (error) {
    console.error(`Error updating video blog with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a video blog
 */
export const deleteVideoBlog = async (id: string): Promise<boolean> => {
  try {
    const response = await apiClient.delete(`/video-blogs/${id}`);

    if (response.data && response.data.status === "success") {
      return true;
    } else {
      console.error("API returned unexpected format:", response.data);
      return false;
    }
  } catch (error) {
    console.error(`Error deleting video blog with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Format duration for display
 */
export const formatDuration = (seconds: number): string => {
  if (!seconds) return '0:00';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
};

// YouTube-specific utility functions

/**
 * Extract YouTube video ID from URL
 */
export const extractYouTubeVideoId = (url: string): string | null => {
  const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
};

/**
 * Get YouTube thumbnail URL
 */
export const getYouTubeThumbnail = (videoId: string, quality: 'default' | 'medium' | 'high' | 'maxres' = 'maxres'): string => {
  return `https://img.youtube.com/vi/${videoId}/${quality}default.jpg`;
};

/**
 * Get YouTube embed URL
 */
export const getYouTubeEmbedUrl = (videoId: string): string => {
  return `https://www.youtube.com/embed/${videoId}`;
};

/**
 * Get unique categories from VLOGS
 */
export const getVideoBlogCategories = async (): Promise<string[]> => {
  try {
    // For now, return default categories. In the future, this could fetch from the backend
    return ['General', 'Craftsmanship', 'Tutorial', 'Behind the Scenes', 'Care Tips'];
  } catch (error) {
    console.error("Error fetching video blog categories:", error);
    return [];
  }
};

/**
 * Create a YouTube video blog quickly
 */
export const createYouTubeVideoBlog = async (
  youtubeUrl: string,
  title: string,
  description: string,
  category: string = "General",
  tags: string[] = []
): Promise<VideoBlogData | null> => {
  try {
    // Extract video ID from URL
    const videoId = extractYouTubeVideoId(youtubeUrl);
    if (!videoId) {
      throw new Error('Invalid YouTube URL');
    }

    // Create video blog data
    const videoBlogData = {
      title,
      description,
      videoUrl: youtubeUrl,
      thumbnailUrl: getYouTubeThumbnail(videoId),
      youtubeUrl,
      youtubeVideoId: videoId,
      category,
      tags
    };

    return await createVideoBlog(videoBlogData);
  } catch (error) {
    console.error("Error creating YouTube video blog:", error);
    return null;
  }
};
