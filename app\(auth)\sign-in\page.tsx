"use client";
import React, { useState, useEffect, Suspense } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import GoogleSignInButton from "@/components/auth/GoogleSignInButton";
import OrDivider from "@/components/auth/OrDivider";
import { useAuth } from "@/contexts/AuthContext";
import PublicRoute from "@/components/auth/PublicRoute";
import PasswordInput from "@/components/ui/PasswordInput";

const SignIn = () => {
  return (
    <Suspense fallback={<div></div>}>
      <SignInContent />
    </Suspense>
  );
};

const SignInContent = () => {
  const { login, loginWithGoogle, isLoading, error } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectPath = searchParams.get("redirect");

  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false,
  });

  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.email || !formData.password) {
      toast.error("Please fill in all fields");
      return;
    }

    await login(formData.email, formData.password);
  };

  const handleGoogleSignIn = async () => {
    await loginWithGoogle();
  };

  return (
    <PublicRoute>
      <div className="bg-card rounded-lg shadow-lg p-8 w-full">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <h2 className="text-2xl font-bold text-center mb-6">Welcome Back</h2>

          {/* Google Sign-In Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            {/* <GoogleSignInButton mode="signin" onSuccess={handleGoogleSignIn} /> */}
          </motion.div>

          {/* <OrDivider text="or sign in with email" /> */}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="email" className="block text-sm font-medium">
                Email Address
              </label>
              <motion.div whileFocus={{ scale: 1.01 }} className="relative">
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all"
                  placeholder="<EMAIL>"
                />
              </motion.div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <label htmlFor="password" className="block text-sm font-medium">
                  Password
                </label>

              </div>
              <PasswordInput
                id="password"
                name="password"
                required
                value={formData.password}
                onChange={handleChange}
                placeholder="••••••••"
              />
            </div>
            <Link
                  href="/forgot-password"
                  className="flex text-xs text-accent hover:underline justify-end"
                >
                  Forgot password?
                </Link>

            <div className="flex items-center">
              <input
                id="rememberMe"
                name="rememberMe"
                type="checkbox"
                checked={formData.rememberMe}
                onChange={handleChange}
                className="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded"
              />
              <label
                htmlFor="rememberMe"
                className="ml-2 block text-sm text-gray-700"
              >
                Remember me
              </label>
            </div>

            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="pt-2"
            >
              <Button
                type="submit"
                variant="default"
                className="w-full py-2"
                disabled={isLoading}
              >
                {isLoading ? "Signing In..." : "Sign In"}
              </Button>
            </motion.div>
          </form>

          <div className="mt-6 text-center text-sm">
            <p>
              Don&apos;t have an account?{" "}
              <Link
                href="/sign-up"
                className="text-accent hover:underline font-medium"
              >
                Sign Up
              </Link>
            </p>
          </div>
        </motion.div>
      </div>
    </PublicRoute>
  );
};

export default SignIn;
