{"name": "chinioti_furniture", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3000", "build": "next build", "start": "next start", "lint": "next lint", "generate-favicons": "node scripts/generate-favicons.js", "prebuild": "npm run generate-favicons"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.2", "axios": "^1.8.4", "chinioti_furniture": "file:", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.6.3", "leaflet": "^1.9.4", "leaflet-defaulticon-compatibility": "^0.1.2", "lucide-react": "^0.487.0", "next": "^15.3.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "react-leaflet": "^5.0.0", "shadcn": "^2.4.0", "sonner": "^2.0.3", "swiper": "^11.2.6", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "fs-extra": "^11.3.0", "sharp": "^0.33.2", "tailwindcss": "^4", "typescript": "^5"}}