'use client';
import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { secondaryFont } from '@/constants/fonts';

const Layout = ({ children }: { children: ReactNode }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background/90 flex flex-col items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <Link href="/">
          <h1
            className={`${secondaryFont} text-4xl text-center text-foreground hover:text-accent transition-colors duration-300`}
          >
            <PERSON><PERSON><PERSON>en Art
          </h1>
        </Link>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="w-full max-w-md"
      >
        {children}
      </motion.div>
    </div>
  );
};

export default Layout;
