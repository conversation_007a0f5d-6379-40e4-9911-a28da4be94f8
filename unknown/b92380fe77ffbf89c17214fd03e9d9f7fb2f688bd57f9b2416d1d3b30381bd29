/**
 * Utility functions for authentication
 */

/**
 * Check if Google authentication is in progress
 * @returns boolean indicating if Google auth is in progress
 */
export const isGoogleAuthInProgress = (): boolean => {
  if (typeof window === 'undefined') return false;
  return sessionStorage.getItem('googleAuthInProgress') === 'true';
};

/**
 * Set Google authentication in progress flag
 */
export const setGoogleAuthInProgress = (): void => {
  if (typeof window === 'undefined') return;
  sessionStorage.setItem('googleAuthInProgress', 'true');
};

/**
 * Clear Google authentication in progress flag
 */
export const clearGoogleAuthInProgress = (): void => {
  if (typeof window === 'undefined') return;
  sessionStorage.removeItem('googleAuthInProgress');
};
