"use client";
import { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { motion } from "framer-motion";
import { useAuth } from "@/contexts/AuthContext";
import { clearGoogleAuthInProgress } from "@/lib/auth/authUtils";

// Loading fallback component
function AuthCallbackLoading() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-white to-gray-50">
      <motion.div
        className="text-center bg-white p-8 rounded-lg shadow-md max-w-md w-full"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            rotate: [0, 0, 0],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatType: "loop",
          }}
          className="mx-auto mb-6 w-16 h-16 rounded-full bg-accent/10 flex items-center justify-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="32"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-accent animate-spin"
          >
            <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
          </svg>
        </motion.div>

        <h1 className="text-2xl font-bold mb-4">Loading Authentication...</h1>
        <p className="text-gray-600">
          Please wait while we process your authentication.
        </p>
      </motion.div>
    </div>
  );
}

export default function AuthCallback() {
  return (
    <Suspense fallback={<AuthCallbackLoading />}>
      <AuthCallbackContent />
    </Suspense>
  );
}

function AuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isProcessing, setIsProcessing] = useState(true);
  const [authStatus, setAuthStatus] = useState<"success" | "error" | null>(
    null
  );
  const { setAuthState } = useAuth();

  // Run once on component mount to process authentication
  useEffect(() => {
    // Check if we've already processed this auth callback
    const hasProcessed =
      typeof window !== "undefined" &&
      sessionStorage.getItem("authCallbackProcessed");

    // This flag helps prevent multiple executions
    let isHandled = false;

    const processAuth = async () => {
      if (isHandled || hasProcessed) return;
      isHandled = true;

      // Set a flag in session storage to prevent reprocessing
      if (typeof window !== "undefined") {
        sessionStorage.setItem("authCallbackProcessed", "true");
      }

      console.log("Starting Google auth callback processing");
      setIsProcessing(true); // Ensure we're in processing state

      try {
        // Clear the Google auth in progress flag
        clearGoogleAuthInProgress();
        console.log("Cleared Google auth in progress flag");

        // Log all search parameters for debugging
        const params = Object.fromEntries(searchParams.entries());
        console.log("Processing auth callback with params:", params);

        // Check for error first
        const error = searchParams.get("error");
        const errorMessage = searchParams.get("errorMessage");

        if (error) {
          setAuthStatus("error");
          toast.error(errorMessage || "Authentication failed");
          setTimeout(() => {
            router.push("/sign-in");
            setIsProcessing(false);
          }, 1500);
          return;
        }

        const token = searchParams.get("token");
        const userId = searchParams.get("userId");
        const name = searchParams.get("name");
        const email = searchParams.get("email");
        const avatar = searchParams.get("avatar");
        const phone = searchParams.get("phone");

        if (token && userId) {
          // Store auth data
          localStorage.setItem("token", token);
          localStorage.setItem("userId", userId);
          localStorage.setItem("name", name || "");
          localStorage.setItem("email", email || "");
          if (avatar) localStorage.setItem("avatar", avatar);
          if (phone) localStorage.setItem("phone", phone);

          // Update auth state directly
          setAuthState({
            user: {
              id: userId,
              name: name || "User",
              email: email || "",
              avatar: avatar || undefined,
              phone: phone || undefined,
            },
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          setAuthStatus("success");
          toast.success("Successfully signed in with Google!");

          // Redirect to home page or dashboard after a short delay
          setTimeout(() => {
            // Clear the session storage flag before redirecting
            if (typeof window !== "undefined") {
              sessionStorage.removeItem("authCallbackProcessed");
            }

            // Use replace instead of push to avoid browser history issues
            router.replace("/");
            setIsProcessing(false);
          }, 1500);
        } else {
          setAuthStatus("error");
          toast.error("Authentication failed - missing required data");
          setTimeout(() => {
            router.replace("/sign-in");
            setIsProcessing(false);
          }, 1500);
        }
      } catch (error) {
        console.error("Auth callback error:", error);
        setAuthStatus("error");
        toast.error("An error occurred during authentication");
        setTimeout(() => {
          router.replace("/sign-in");
          setIsProcessing(false);
        }, 1500);
      }
    };

    // Process auth if we have search params, haven't started processing yet, and haven't processed before
    if (searchParams.toString() && authStatus === null && !hasProcessed) {
      console.log("Starting auth processing with status:", authStatus);
      processAuth();
    } else {
      console.log(
        "Skipping auth processing. Status:",
        authStatus,
        "Params:",
        searchParams.toString() ? "present" : "absent",
        "Already processed:",
        hasProcessed ? "yes" : "no"
      );
    }

    // Cleanup function to prevent memory leaks
    return () => {
      isHandled = true;

      // Clear the session storage flag when the component is unmounted
      // This is important for cases where the user might try to authenticate again
      if (typeof window !== "undefined") {
        sessionStorage.removeItem("authCallbackProcessed");
      }
    };
  }, [router, searchParams, setAuthState]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-white to-gray-50">
      <motion.div
        className="text-center bg-white p-8 rounded-lg shadow-md max-w-md w-full"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            rotate: [0, 0, 0],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatType: "loop",
          }}
          className="mx-auto mb-6 w-16 h-16 rounded-full bg-accent/10 flex items-center justify-center"
        >
          {authStatus === "success" ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          ) : authStatus === "error" ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-red-500"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="15" y1="9" x2="9" y2="15"></line>
              <line x1="9" y1="9" x2="15" y2="15"></line>
            </svg>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-accent animate-spin"
            >
              <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
            </svg>
          )}
        </motion.div>

        <h1 className="text-2xl font-bold mb-4">
          {authStatus === "success"
            ? "Authentication Successful!"
            : authStatus === "error"
            ? "Authentication Failed"
            : "Processing Authentication..."}
        </h1>

        <p className="text-gray-600">
          {authStatus === "success"
            ? "You will be redirected to the homepage shortly."
            : authStatus === "error"
            ? "There was a problem with your authentication. Redirecting you back to sign in."
            : "Please wait while we complete your authentication."}
        </p>
      </motion.div>
    </div>
  );
}
