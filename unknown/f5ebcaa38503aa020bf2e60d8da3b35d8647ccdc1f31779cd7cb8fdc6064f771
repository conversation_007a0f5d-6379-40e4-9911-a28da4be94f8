import { CartItem } from "./cart";

export type OrderStatus =
  | "pending"
  | "processing"
  | "shipped"
  | "delivered"
  | "cancelled";

export interface OrderCustomer {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

export interface OrderShipping {
  address: string;
  city: string;
  postalCode?: string;
}

export interface Order {
  id: string;
  items: CartItem[];
  total: number;
  subtotal: number;
  discount: number;
  customer: OrderCustomer;
  shipping: OrderShipping;
  paymentMethod: "cash" | "bank";
  status: OrderStatus;
  createdAt: string;
  updatedAt?: string;
}

export interface OrderContextType {
  orders: Order[];
  isLoading: boolean;
  getOrder: (id: string) => Promise<Order | undefined>;
  createOrder: (order: Omit<Order, "id" | "createdAt">) => Promise<Order>;
  updateOrderStatus: (id: string, status: OrderStatus) => Promise<void>;
  cancelOrder: (id: string) => Promise<void>;
}
