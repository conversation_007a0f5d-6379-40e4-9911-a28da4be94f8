"use client";
import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Menu, X, Home, ShoppingBag } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { getUniqueCategories } from "@/apis/products";

interface NavItem {
  name: string;
  path: string;
}

const Navbar = () => {
  const [activeItem, setActiveItem] = useState<number | null>(null);
  const [hoveredItem, setHoveredItem] = useState<number | null>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [navItems, setNavItems] = useState<NavItem[]>([
    { name: "All Products", path: "/products" },
  ]);
  const [isLoading, setIsLoading] = useState(true);
  const pathname = usePathname();


  // Fetch categories from products API
  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoading(true);
      try {
        // Get unique categories from products
        const categories = await getUniqueCategories();

        // Create nav items from categories
        const categoryNavItems = categories.map((category) => ({
          name: category.charAt(0).toUpperCase() + category.slice(1), // Capitalize first letter
          path: `/products?category=${category.toLowerCase()}`,
        }));

        // Add "All Products" at the beginning
        setNavItems([
          { name: "All Products", path: "/products" },
          ...categoryNavItems,
        ]);
      } catch (error) {
        console.error("Error fetching categories for navbar:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);


  // Set active item based on current path
  useEffect(() => {
    const categoryParam = new URLSearchParams(pathname.split("?")[1] || "").get(
      "category"
    );

    if (pathname === "/products" && !categoryParam) {
      // If we're on the products page with no category, set "All Products" as active
      setActiveItem(0);
    } else if (categoryParam) {
      // If a category is selected, find the matching nav item
      const index = navItems.findIndex((item) =>
        item.path.includes(`category=${categoryParam.toLowerCase()}`)
      );
      if (index !== -1) {
        setActiveItem(index);
      }
    } else {
      // Reset active item if not on products page
      setActiveItem(null);
    }
  }, [pathname, navItems]);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <div className="w-full py-1 bg-gradient-to-r from-accent/5 via-accent/10 to-accent/5 border-y border-gray-200 shadow-sm">
      <div className="container mx-auto px-4 sm:px-6">
        {/* Mobile Menu Button */}
        <div className="flex md:hidden justify-center mb-2">
          <button
            onClick={toggleMobileMenu}
            className="flex items-center gap-2 px-3 py-1.5 bg-white/50 rounded-md text-sm font-medium text-gray-700"
            aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
          >
            {mobileMenuOpen ? (
              <>
                <X size={18} />
                <span>Close</span>
              </>
            ) : (
              <>
                <Menu size={18} />
                <span>Categories</span>
              </>
            )}
          </button>
        </div>

        {/* Desktop Navigation */}
        {isLoading ? (
          <div className="hidden md:flex justify-center w-full">
            <div className="animate-pulse bg-gray-200 h-8 w-3/4 rounded"></div>
          </div>
        ) : (
          <ul className="hidden md:flex flex-row flex-wrap justify-center items-center gap-2">
            {navItems.map((item, idx) => (
              <Link href={item.path} key={idx} className="outline-none">
                <motion.li
                  className={`relative px-3 sm:px-5 py-2 font-medium cursor-pointer transition-all duration-300 ease-in-out
                    ${
                      activeItem === idx
                        ? "text-accent bg-white/50 rounded-md shadow-sm"
                        : "text-gray-700"
                    }
                    hover:text-accent`}
                  onHoverStart={() => setHoveredItem(idx)}
                  onHoverEnd={() => setHoveredItem(null)}
                  onClick={() => setActiveItem(idx)}
                  whileHover={{ y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 17 }}
                >
                  {item.name}

                  {/* Bottom border animation on hover */}
                  {hoveredItem === idx && activeItem !== idx && (
                    <motion.div
                      className="absolute bottom-0 left-0 h-0.5 bg-accent"
                      initial={{ width: 0 }}
                      animate={{ width: "100%" }}
                      transition={{ duration: 0.2 }}
                    />
                  )}


                  {/* Active item indicator */}
                  {activeItem === idx && (
                    <motion.div
                      className="absolute -bottom-3 left-0 right-0 mx-auto w-1.5 h-1.5 bg-accent rounded-full"
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.2 }}
                    />
                  )}
                </motion.li>
              </Link>
            ))}
          </ul>
        )}

        {/* Mobile Navigation */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden md:hidden"
            >
              {isLoading ? (
                <div className="grid grid-cols-2 gap-2 py-2">
                  {[...Array(6)].map((_, idx) => (
                    <div
                      key={idx}
                      className="animate-pulse bg-gray-200 h-10 rounded"
                    ></div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-2 py-2">
                  {navItems.map((item, idx) => (
                    <Link href={item.path} key={idx} className="outline-none">
                      <motion.div
                        className={`px-3 py-2 text-center font-medium cursor-pointer rounded-md transition-all duration-300
                          ${
                            activeItem === idx
                              ? "text-accent bg-white/80 shadow-sm"
                              : "text-gray-700 bg-white/30"
                          }
                          hover:bg-white/50 hover:text-accent`}
                        onClick={() => {
                          setActiveItem(idx);
                          setMobileMenuOpen(false);
                        }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {item.name}
                      </motion.div>
                    </Link>
                  ))}
                </div>
              )}

            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Navbar;
