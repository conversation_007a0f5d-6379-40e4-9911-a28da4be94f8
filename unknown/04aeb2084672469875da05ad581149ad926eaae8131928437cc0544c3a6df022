"use client";
import React from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import { useAuth } from "@/contexts/AuthContext";

interface GoogleSignInButtonProps {
  text?: string;
  mode?: "signin" | "signup";
  className?: string;
  onSuccess?: () => void;
  onError?: (error: unknown) => void;
}

const GoogleSignInButton: React.FC<GoogleSignInButtonProps> = ({
  text = "Continue with Google",
  mode = "signin",
  className = "",
  onSuccess,
  onError,
}) => {
  const { loginWithGoogle, isLoading } = useAuth();

  const handleGoogleSignIn = async () => {
    try {
      console.log(`Initiating Google ${mode} process`);
      await loginWithGoogle();
      console.log("Google login function called successfully");

      // Call onSuccess callback if provided
      if (onSuccess) {
        console.log("Calling onSuccess callback");
        onSuccess();
      }
    } catch (error) {
      console.error(`Google ${mode} error:`, error);

      // Call onError callback if provided
      if (onError) {
        console.log("Calling onError callback");
        onError(error);
      }
    }
  };

  return (
    <motion.button
      onClick={handleGoogleSignIn}
      disabled={isLoading}
      className={`w-full flex items-center justify-center gap-2 px-4 py-2.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-accent/30 disabled:opacity-70 disabled:cursor-not-allowed transition-all duration-200 ${className}`}
      whileHover={{ scale: 1.01 }}
      whileTap={{ scale: 0.98 }}
    >
      {isLoading ? (
        <div className="h-5 w-5 border-2 border-gray-300 border-t-accent rounded-full animate-spin" />
      ) : (
        <Image
          src="/assets/icons/google-logo.svg"
          alt="Google logo"
          width={20}
          height={20}
          className="mr-1"
        />
      )}
      <span>
        {isLoading
          ? `${mode === "signin" ? "Signing in" : "Signing up"}...`
          : text}
      </span>
    </motion.button>
  );
};

export default GoogleSignInButton;
