import apiClient from "@/lib/api/apiClient";
import { User } from "@/types/user";

/**
 * Get the current user's profile
 */
export const getCurrentUser = async (): Promise<User | null> => {
  try {
    // Get user ID from localStorage
    const userId = localStorage.getItem("userId");
    if (!userId) {
      console.error("No user ID found in localStorage");
      return null;
    }

    const response = await apiClient.get(`/users/${userId}`);
    if (response.data) {
      return {
        id: response.data._id || response.data.id,
        name: response.data.name,
        email: response.data.email,
        phone: response.data.phone,
        avatar: response.data.avatar,
      };
    }
    return null;
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return null;
  }
};

/**
 * Update user profile
 */
export const updateUserProfile = async (
  userId: string,
  userData: Partial<User>
): Promise<User | null> => {
  try {
    // Don't send the id in the request body
    const { id, ...dataToUpdate } = userData;

    const response = await apiClient.put(`/users/${userId}`, dataToUpdate);
    if (response.data) {
      // Update localStorage with new user data
      if (response.data.name) {
        localStorage.setItem("name", response.data.name);
      }
      if (response.data.email) {
        localStorage.setItem("email", response.data.email);
      }

      return {
        id: response.data._id || response.data.id,
        name: response.data.name,
        email: response.data.email,
        phone: response.data.phone,
        avatar: response.data.avatar,
      };
    }
    return null;
  } catch (error) {
    console.error("Error updating user profile:", error);
    throw error;
  }
};

/**
 * Change user password
 */
export const changePassword = async (
  userId: string,
  currentPassword: string,
  newPassword: string
): Promise<boolean> => {
  try {
    console.log("Changing password for user:", userId);

    // Use the auth endpoint pattern instead of users endpoint
    const response = await apiClient.patch(`/auth/change-password`, {
      userId,
      currentPassword,
      newPassword,
    });

    console.log("Password change response:", response.status);
    return response.status === 200;
  } catch (error: any) {
    console.error("Error changing password:", error);
    // Log more detailed error information
    if (error.response) {
      console.error("Error response data:", error.response.data);
      console.error("Error response status:", error.response.status);
    } else if (error.request) {
      console.error("No response received:", error.request);
    } else {
      console.error("Error message:", error.message);
    }
    throw error;
  }
};
