"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { Order, OrderContextType, OrderStatus } from "@/types/order";
import { toast } from "sonner";
import { useAuth } from "./AuthContext";
import * as orderApi from "@/apis/orders";

// Create context
const OrderContext = createContext<OrderContextType | undefined>(undefined);

// Provider component
export const OrderProvider = ({ children }: { children: ReactNode }) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { user, isAuthenticated } = useAuth();

  // Fetch orders from API when user is authenticated
  useEffect(() => {
    const fetchOrders = async () => {
      if (isAuthenticated && user?.id) {
        setIsLoading(true);
        try {
          const userOrders = await orderApi.getUserOrders(user.id);
          setOrders(userOrders);
        } catch (error) {
          console.error("Failed to fetch orders:", error);
          toast.error("Failed to load your orders. Please try again later.");
        } finally {
          setIsLoading(false);
        }
      } else {
        // Clear orders when user is not authenticated
        setOrders([]);
      }
    };

    fetchOrders();
  }, [isAuthenticated, user]);

  // Get a specific order by ID
  const getOrder = async (id: string): Promise<Order | undefined> => {
    // First check if the order is already in state
    const cachedOrder = orders.find((order) => order.id === id);
    if (cachedOrder) return cachedOrder;

    // If not found in state, try to fetch from API
    if (isAuthenticated) {
      try {
        const order = await orderApi.getOrderById(id);
        return order || undefined;
      } catch (error) {
        console.error(`Failed to fetch order ${id}:`, error);
        return undefined;
      }
    }

    return undefined;
  };

  // Create a new order
  const createOrder = async (
    orderData: Omit<Order, "id" | "createdAt">
  ): Promise<Order> => {
    if (!isAuthenticated || !user?.id) {
      throw new Error("You must be logged in to create an order");
    }

    try {
      const newOrder = await orderApi.createOrder(orderData, user.id);

      // Update local state with the new order
      setOrders((prevOrders) => [...prevOrders, newOrder]);

      toast.success("Order created successfully!");
      return newOrder;
    } catch (error) {
      console.error("Failed to create order:", error);
      toast.error("Failed to create order. Please try again.");
      throw error;
    }
  };

  // Update order status
  const updateOrderStatus = async (
    id: string,
    status: OrderStatus
  ): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error("You must be logged in to update an order");
    }

    try {
      const updatedOrder = await orderApi.updateOrderStatus(id, status);

      if (updatedOrder) {
        // Update local state
        setOrders((prevOrders) =>
          prevOrders.map((order) => (order.id === id ? updatedOrder : order))
        );

        toast.success(`Order status updated to ${status}`);
      }
    } catch (error) {
      console.error(`Failed to update order ${id} status:`, error);
      toast.error("Failed to update order status. Please try again.");
    }
  };

  // Cancel an order
  const cancelOrder = async (id: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error("You must be logged in to cancel an order");
    }

    try {
      const cancelledOrder = await orderApi.cancelOrder(id);

      if (cancelledOrder) {
        // Update local state
        setOrders((prevOrders) =>
          prevOrders.map((order) => (order.id === id ? cancelledOrder : order))
        );

        toast.info("Order cancelled");
      }
    } catch (error) {
      console.error(`Failed to cancel order ${id}:`, error);
      toast.error("Failed to cancel order. Please try again.");
    }
  };

  return (
    <OrderContext.Provider
      value={{
        orders,
        isLoading,
        getOrder,
        createOrder,
        updateOrderStatus,
        cancelOrder,
      }}
    >
      {children}
    </OrderContext.Provider>
  );
};

// Custom hook to use order context
export const useOrders = (): OrderContextType => {
  const context = useContext(OrderContext);
  if (context === undefined) {
    throw new Error("useOrders must be used within an OrderProvider");
  }
  return context;
};
