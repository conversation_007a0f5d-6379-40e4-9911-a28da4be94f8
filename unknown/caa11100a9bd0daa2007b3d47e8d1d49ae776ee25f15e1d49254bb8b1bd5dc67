/**
 * This script generates various favicon formats from the source SVG
 * 
 * To use this script:
 * 1. Install sharp: npm install --save-dev sharp
 * 2. Run: node scripts/generate-favicons.js
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

const SOURCE_SVG = path.join(__dirname, '../public/favicon.svg');
const PUBLIC_DIR = path.join(__dirname, '../public');

// Sizes for various icons
const SIZES = {
  favicon: [16, 32, 48, 64],
  apple: [180],
  android: [192, 512],
};

async function generateFavicons() {
  try {
    console.log('Generating favicons from SVG...');
    
    // Check if source SVG exists
    if (!fs.existsSync(SOURCE_SVG)) {
      console.error('Source SVG not found:', SOURCE_SVG);
      return;
    }
    
    // Read the SVG file
    const svgBuffer = fs.readFileSync(SOURCE_SVG);
    
    // Generate favicon.ico (multi-size ICO file)
    const faviconSizes = SIZES.favicon.map(size => {
      return sharp(svgBuffer)
        .resize(size, size)
        .toBuffer();
    });
    
    // Wait for all favicon sizes to be generated
    const faviconBuffers = await Promise.all(faviconSizes);
    
    // Use the first buffer as the favicon.ico
    // Note: In a production environment, you would use a library that can create
    // multi-size ICO files, but for this example we'll just use the 16x16 size
    fs.writeFileSync(path.join(PUBLIC_DIR, 'favicon.ico'), faviconBuffers[0]);
    
    // Generate Apple touch icon
    await sharp(svgBuffer)
      .resize(SIZES.apple[0], SIZES.apple[0])
      .toFile(path.join(PUBLIC_DIR, 'apple-touch-icon.png'));
    
    // Generate Android icons
    for (const size of SIZES.android) {
      await sharp(svgBuffer)
        .resize(size, size)
        .toFile(path.join(PUBLIC_DIR, `logo${size}.png`));
    }
    
    // Generate Open Graph image (if it doesn't exist as a PNG already)
    const ogImagePath = path.join(PUBLIC_DIR, 'og-image.jpg');
    if (!fs.existsSync(ogImagePath)) {
      const ogSvgPath = path.join(PUBLIC_DIR, 'og-image.svg');
      if (fs.existsSync(ogSvgPath)) {
        await sharp(fs.readFileSync(ogSvgPath))
          .resize(1200, 630)
          .toFile(ogImagePath);
      }
    }
    
    // Generate Twitter image (if it doesn't exist as a PNG already)
    const twitterImagePath = path.join(PUBLIC_DIR, 'twitter-image.jpg');
    if (!fs.existsSync(twitterImagePath)) {
      const twitterSvgPath = path.join(PUBLIC_DIR, 'twitter-image.svg');
      if (fs.existsSync(twitterSvgPath)) {
        await sharp(fs.readFileSync(twitterSvgPath))
          .resize(1200, 600)
          .toFile(twitterImagePath);
      }
    }
    
    console.log('Favicon generation complete!');
  } catch (error) {
    console.error('Error generating favicons:', error);
  }
}

generateFavicons();
