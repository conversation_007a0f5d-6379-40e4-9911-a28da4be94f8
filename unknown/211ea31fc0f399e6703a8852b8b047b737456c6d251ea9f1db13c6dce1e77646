"use client";

import React, { useRef, useEffect } from "react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { useCurrency } from "@/contexts/CurrencyContext";
import { useRouter } from "next/navigation";
import { SearchTermSuggestion } from "@/utils/searchUtils";
import { CiSearch } from "react-icons/ci";

interface SearchSuggestionsProps {
  termSuggestions: SearchTermSuggestion[];
  productSuggestions: ProductData[];
  isVisible: boolean;
  onSelectProduct: (product: ProductData) => void;
  onSelectTerm: (term: string) => void;
  onClose: () => void;
  highlightedIndex: number;
  highlightedSection: "terms" | "products";
  setHighlightedIndex: (index: number, section: "terms" | "products") => void;
  maxProductSuggestions?: number;
  searchQuery?: string;
}

const SearchSuggestions: React.FC<SearchSuggestionsProps> = ({
  termSuggestions,
  productSuggestions,
  isVisible,
  onSelectProduct,
  onSelectTerm,
  onClose,
  highlightedIndex,
  highlightedSection,
  setHighlightedIndex,
  maxProductSuggestions = 3,
  searchQuery = "",
}) => {
  const router = useRouter();
  const { formatPrice } = useCurrency();
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Determine if we have any suggestions to show
  const hasTermSuggestions = termSuggestions.length > 0;
  const hasProductSuggestions = productSuggestions.length > 0;
  const hasSuggestions = hasTermSuggestions || hasProductSuggestions;

  // Limit the number of product suggestions shown
  const limitedProductSuggestions = productSuggestions.slice(
    0,
    maxProductSuggestions
  );

  // Note: Click outside detection is now handled by the parent SearchField component

  // Handle product suggestion selection
  const handleSelectProduct = (product: ProductData) => {
    onSelectProduct(product);
  };

  // Handle term suggestion selection
  const handleSelectTerm = (term: string) => {
    onSelectTerm(term);
  };

  // Handle "View all results" click
  const handleViewAllResults = () => {
    router.push(`/products?query=${encodeURIComponent(searchQuery)}`);
    onClose();
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          ref={suggestionsRef}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
          className="absolute z-50 w-full mt-1 bg-white rounded-md shadow-lg border border-gray-200 overflow-hidden"
        >
          <div className="max-h-[400px] overflow-y-auto">
            {/* Search Term Suggestions */}
            {hasTermSuggestions && (
              <div className="py-1">
                <div className="px-3 py-1 text-xs font-medium text-gray-500 bg-gray-50">
                  Suggested searches
                </div>
                {termSuggestions.map((term, index) => (
                  <div
                    key={`term-${term.term}`}
                    className={`px-4 py-2 cursor-pointer transition-colors ${
                      highlightedSection === "terms" &&
                      highlightedIndex === index
                        ? "bg-accent/10 text-accent"
                        : "hover:bg-gray-50"
                    }`}
                    onClick={() => handleSelectTerm(term.term)}
                    onMouseEnter={() => setHighlightedIndex(index, "terms")}
                  >
                    <div className="flex items-center gap-2">
                      <CiSearch className="text-gray-400" size={16} />
                      <span className="text-sm">
                        {term.term}
                        {term.category && (
                          <span className="text-xs text-gray-500 ml-1">
                            in {term.category}
                          </span>
                        )}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Product Suggestions */}
            {hasProductSuggestions && (
              <div
                className={`py-1 ${
                  hasTermSuggestions ? "border-t border-gray-100" : ""
                }`}
              >
                <div className="px-3 py-1 text-xs font-medium text-gray-500 bg-gray-50">
                  {searchQuery.trim() === "" ? "Popular Products" : "Products"}
                </div>
                {limitedProductSuggestions.map((product, index) => (
                  <div
                    key={`product-${product.id}`}
                    className={`px-4 py-2 cursor-pointer transition-colors ${
                      highlightedSection === "products" &&
                      highlightedIndex === index
                        ? "bg-accent/10 text-accent"
                        : "hover:bg-gray-50"
                    }`}
                    onClick={() => handleSelectProduct(product)}
                    onMouseEnter={() => setHighlightedIndex(index, "products")}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 flex-shrink-0 rounded-md overflow-hidden bg-gray-100 relative">
                        <Image
                          src={product.image || "/assets/placeholder.png"}
                          alt={product.title}
                          fill
                          sizes="48px"
                          className="object-cover"
                        />
                      </div>
                      <div className="flex-grow min-w-0">
                        <p className="text-sm font-medium truncate">
                          {product.title}
                        </p>
                        <p className="text-xs text-gray-500 truncate">
                          {product.category}
                        </p>
                      </div>
                      <div className="flex-shrink-0">
                        <p className="text-sm font-semibold">
                          {formatPrice(product.price)}
                        </p>
                        {parseInt(product.discount) > 0 && (
                          <p className="text-xs text-green-600">
                            {product.discount}% off
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* No Results Message */}
            {!hasSuggestions && searchQuery.trim() !== "" && (
              <div className="px-4 py-3 text-center text-gray-500">
                <p className="text-sm">No suggestions found for &quot;{searchQuery}&quot;</p>
                <p className="text-xs mt-1">Try a different search term</p>
              </div>
            )}

            {/* View All Results */}
            {searchQuery.trim() !== "" && (
              <div className={`px-4 py-2 ${hasSuggestions ? "border-t border-gray-100" : ""}`}>
                <button
                  onClick={handleViewAllResults}
                  className="text-sm text-accent hover:text-accent/80 font-medium w-full text-left flex items-center gap-2"
                >
                  <CiSearch size={16} />
                  <span>Search for &quot;{searchQuery}&quot;</span>
                </button>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SearchSuggestions;
