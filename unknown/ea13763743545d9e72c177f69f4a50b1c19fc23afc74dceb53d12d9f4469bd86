'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { User, AuthState, AuthContextType } from '@/types/user';
import apiClient from '@/lib/api/apiClient';
import {
  setGoogleAuthInProgress,
  clearGoogleAuthInProgress,
  isGoogleAuthInProgress,
} from '@/lib/auth/authUtils';

// Initial auth state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [authState, setAuthState] = useState<AuthState>(initialState);
  const router = useRouter();

  // Check if user is already logged in on initial load
  useEffect(() => {
    const checkAuth = () => {
      try {
        const token = localStorage.getItem('token');
        const userId = localStorage.getItem('userId');
        const name = localStorage.getItem('name');
        const email = localStorage.getItem('email');
        const avatar = localStorage.getItem('avatar');

        if (token && userId) {
          setAuthState({
            user: {
              id: userId,
              name: name || 'User',
              email: email || '',
              avatar: avatar || undefined,
            },
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } else {
          setAuthState({
            ...initialState,
            isLoading: false,
          });
        }
      } catch (error) {
        console.error('Auth check error:', error);
        setAuthState({
          ...initialState,
          isLoading: false,
        });
      }
    };

    checkAuth();
  }, []);

  // Login with email and password
  const login = async (email: string, password: string) => {
    setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await apiClient.post('/auth/login', {
        email,
        password,
      });

      if (response.data.status === 'success') {
        const { user, token } = response.data.data;

        // Store auth data in localStorage
        localStorage.setItem('token', token);
        localStorage.setItem('userId', user.id);
        localStorage.setItem('name', user.name);
        localStorage.setItem('email', user.email);

        // Update auth state
        setAuthState({
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
          },
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        toast.success('Logged in successfully!');
        router.push('/');
      } else {
        throw new Error('Login failed');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      const errorMessage =
        error.response?.data?.message || 'Invalid email or password';
      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      toast.error(errorMessage);
    }
  };

  // Login with Google
  const loginWithGoogle = async () => {
    console.log('Starting Google login process');
    setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      // Store a flag to indicate we're in the middle of Google auth
      // This helps prevent infinite redirects
      setGoogleAuthInProgress();
      console.log('Set Google auth in progress flag');

      // Construct the Google auth URL
      const googleAuthUrl = `/api/auth/google`;
      console.log('Redirecting to Google auth URL:', googleAuthUrl);

      // Redirect to the backend Google auth endpoint
      window.location.href = googleAuthUrl;

      // Note: The rest of the authentication process will be handled by the
      // auth-callback page after Google redirects back to our application

      // We don't update the auth state here because the page will be redirected
      return;
    } catch (error) {
      // Clear the auth in progress flag
      clearGoogleAuthInProgress();
      console.log('Cleared Google auth in progress flag due to error');

      console.error('Google login error:', error);
      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
        error: 'Failed to log in with Google',
      }));
      toast.error('Failed to log in with Google. Please try again.');
    }
  };

  // Sign up with email and password
  const signup = async (
    name: string,
    email: string,
    password: string,
    phone: string = '00000000000'
  ) => {
    setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await apiClient.post('/auth/register', {
        name,
        email,
        password,
        phone,
      });

      if (response.data.status === 'success') {
        const { user, token } = response.data.data;

        // Store auth data in localStorage
        localStorage.setItem('token', token);
        localStorage.setItem('userId', user.id);
        localStorage.setItem('name', user.name);
        localStorage.setItem('email', user.email);

        // Update auth state
        setAuthState({
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
          },
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        toast.success('Account created successfully!');
        router.push('/');
      } else {
        throw new Error('Signup failed');
      }
    } catch (error: any) {
      console.error('Signup error:', error);
      const errorMessage =
        error.response?.data?.message || 'Failed to create account';
      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      toast.error(errorMessage);
    }
  };

  // Logout
  const logout = () => {
    // Clear auth data from localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('userId');
    localStorage.removeItem('name');
    localStorage.removeItem('email');
    localStorage.removeItem('avatar');

    // Reset auth state
    setAuthState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });

    toast.info('Logged out successfully');
    router.push('/');
  };

  // Clear error
  const clearError = () => {
    setAuthState((prev) => ({ ...prev, error: null }));
  };

  // Forgot Password
  const forgotPassword = async (email: string) => {
    setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await apiClient.post(
        '/auth/forgot-password',
        { email }
      );

      if (response.data.status === 'success') {
        toast.success('Password reset OTP sent to your email');
      } else {
        throw new Error('Failed to send reset email');
      }

      setAuthState((prev) => ({ ...prev, isLoading: false }));
    } catch (error) {
      console.error('Forgot password error:', error);
      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
        error: 'Failed to send reset email',
      }));
      toast.error('Failed to send reset email. Please try again.');
    }
  };

  // Verify OTP
  const verifyOTP = async (email: string, otp: string): Promise<string> => {
    setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await apiClient.post('/auth/verify-otp', {
        email,
        otp,
      });

      if (response.data.status === 'success') {
        toast.success('OTP verified successfully');
        setAuthState((prev) => ({ ...prev, isLoading: false }));
        return response.data.resetToken;
      } else {
        throw new Error('Invalid OTP');
      }
    } catch (error) {
      console.error('Verify OTP error:', error);
      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
        error: 'Invalid or expired OTP',
      }));
      toast.error('Invalid or expired OTP. Please try again.');
      return '';
    }
  };

  // Reset Password
  const resetPassword = async (resetToken: string, password: string) => {
    setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await apiClient.post('/auth/reset-password', {
        resetToken,
        password,
      });

      if (response.data.status === 'success') {
        toast.success('Password reset successfully');
        setAuthState((prev) => ({ ...prev, isLoading: false }));
        router.push('/sign-in');
      } else {
        throw new Error('Failed to reset password');
      }
    } catch (error) {
      console.error('Reset password error:', error);
      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
        error: 'Failed to reset password',
      }));
      toast.error('Failed to reset password. Please try again.');
    }
  };

  // Function to directly set auth state (used by auth-callback)
  const setAuthStateDirectly = (state: AuthState) => {
    setAuthState(state);
  };

  return (
    <AuthContext.Provider
      value={{
        ...authState,
        login,
        loginWithGoogle,
        signup,
        logout,
        clearError,
        forgotPassword,
        verifyOTP,
        resetPassword,
        setAuthState: setAuthStateDirectly,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
