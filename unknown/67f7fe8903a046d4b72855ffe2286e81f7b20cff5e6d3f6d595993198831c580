import { motion } from "framer-motion";
import Link from "next/link";
import React from "react";

const AnimatedButton = ({
  title,
  path,
  icon,
}: {
  title: string;
  path: string;
  icon: React.ReactNode;
}) => {
  return (
    <div className="order-3 md:order-1 flex justify-center md:justify-start">
      <Link href={path}>
        <motion.div
          className="inline-flex items-center gap-2 px-4 py-2 bg-accent/10 hover:bg-accent/20 text-accent font-medium rounded-md transition-colors duration-300"
          whileHover={{ x: 5 }}
          whileTap={{ scale: 0.98 }}
          transition={{ type: "spring", stiffness: 400 }}
        >
          <span>{title}</span>
          {icon}
        </motion.div>
      </Link>
    </div>
  );
};

export default AnimatedButton;
