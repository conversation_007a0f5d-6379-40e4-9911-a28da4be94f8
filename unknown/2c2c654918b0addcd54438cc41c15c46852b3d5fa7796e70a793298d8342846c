"use client";

import <PERSON>ript from "next/script";
import { siteConfig } from "@/config/site";

interface OrganizationJsonLdProps {
  url: string;
}

/**
 * Generates JSON-LD structured data for the organization
 * This helps search engines understand the business information
 * and can enhance search results with rich snippets
 */
const OrganizationJsonLd = ({ url }: OrganizationJsonLdProps) => {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: siteConfig.name,
    url: url,
    logo: `${url}/logo.svg`,
    sameAs: [
      siteConfig.social.facebook,
      siteConfig.social.instagram,
      siteConfig.social.twitter,
    ],
    contactPoint: {
      "@type": "ContactPoint",
      telephone: siteConfig.contact.phone,
      contactType: "customer service",
      availableLanguage: ["English", "Urdu"],
    },
    address: {
      "@type": "PostalAddress",
      addressLocality: siteConfig.contact.address.city,
      addressRegion: siteConfig.contact.address.region,
      addressCountry: siteConfig.contact.address.country,
    },
    description: siteConfig.description,
  };

  return (
    <Script id="organization-jsonld" type="application/ld+json">
      {JSON.stringify(jsonLd)}
    </Script>
  );
};

export default OrganizationJsonLd;
