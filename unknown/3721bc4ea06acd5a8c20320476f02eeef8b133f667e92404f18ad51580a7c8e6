interface DropdownItem {
  id: string;
  label: string;
  disabled?: boolean;
}

interface DropDownProps {
  items: DropdownItem[];
  label?: string;
  buttonText: string;
  defaultSelectedItem: DropdownItem;
  onSelectionChange: (selectedItem: DropdownItem) => void;
}
type HeroType = "view" | "buy";

interface ProductData {
  id: string;
  type: HeroType;
  video?: string;
  images: string[];
  image?: string;
  title: string;
  category: string;
  available: boolean;
  discount: string;
  price: number;
  description?: string;
  quantity?: number;
  created?: string;
  name?: string;
}

interface Category {
  id: string;
  name: string;
  image: string[];
}

interface InfoCardProps {
  title: string;
  desc: string;
  button_text: string;
  link?: string;
}

interface FeatureCardProps extends InfoCardProps {
  image: string;
}
