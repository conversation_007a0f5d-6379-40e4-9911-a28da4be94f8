"use client";

import { useEffect } from "react";

/**
 * Accessibility Enhancer Component
 * Implements ARIA labels, keyboard navigation, and mobile-first responsive design
 * Ensures 44px touch targets and proper accessibility standards
 */
export default function AccessibilityEnhancer() {
  useEffect(() => {
    // Enhance keyboard navigation
    const enhanceKeyboardNavigation = () => {
      // Add skip to main content link
      const skipLink = document.createElement("a");
      skipLink.href = "#main-content";
      skipLink.textContent = "Skip to main content";
      skipLink.className = "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-accent text-white px-4 py-2 rounded z-50";
      skipLink.style.cssText = `
        position: absolute;
        left: -10000px;
        top: auto;
        width: 1px;
        height: 1px;
        overflow: hidden;
      `;
      
      skipLink.addEventListener("focus", () => {
        skipLink.style.cssText = `
          position: absolute;
          top: 1rem;
          left: 1rem;
          width: auto;
          height: auto;
          overflow: visible;
          background-color: #8B4513;
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 0.25rem;
          z-index: 50;
          text-decoration: none;
        `;
      });
      
      skipLink.addEventListener("blur", () => {
        skipLink.style.cssText = `
          position: absolute;
          left: -10000px;
          top: auto;
          width: 1px;
          height: 1px;
          overflow: hidden;
        `;
      });
      
      document.body.insertBefore(skipLink, document.body.firstChild);
    };

    // Enhance touch targets for mobile
    const enhanceTouchTargets = () => {
      const interactiveElements = document.querySelectorAll(
        'button, a, input, select, textarea, [role="button"], [role="link"], [tabindex]'
      );
      
      interactiveElements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        const computedStyle = window.getComputedStyle(htmlElement);
        const height = parseInt(computedStyle.height);
        const width = parseInt(computedStyle.width);
        
        // Ensure minimum 44px touch target
        if (height < 44 || width < 44) {
          htmlElement.style.minHeight = "44px";
          htmlElement.style.minWidth = "44px";
          htmlElement.style.display = htmlElement.style.display || "inline-flex";
          htmlElement.style.alignItems = "center";
          htmlElement.style.justifyContent = "center";
        }
      });
    };

    // Add ARIA labels to images without alt text
    const enhanceImageAccessibility = () => {
      const images = document.querySelectorAll("img");
      images.forEach((img) => {
        if (!img.alt && !img.getAttribute("aria-label")) {
          // Generate descriptive alt text based on src or context
          const src = img.src;
          let altText = "Image";
          
          if (src.includes("furniture")) {
            altText = "Handcrafted wooden furniture from Chiniot, Pakistan";
          } else if (src.includes("logo")) {
            altText = "Chinioti Wooden Art logo";
          } else if (src.includes("hero") || src.includes("bg")) {
            altText = "Traditional Chiniot furniture craftsman at work";
          } else if (src.includes("product")) {
            altText = "Premium handcrafted wooden furniture piece";
          }
          
          img.alt = altText;
        }
        
        // Add loading attribute for better performance
        if (!img.getAttribute("loading")) {
          img.loading = "lazy";
        }
      });
    };

    // Enhance form accessibility
    const enhanceFormAccessibility = () => {
      const forms = document.querySelectorAll("form");
      forms.forEach((form) => {
        // Add form labels and descriptions
        const inputs = form.querySelectorAll("input, select, textarea");
        inputs.forEach((input) => {
          const htmlInput = input as HTMLInputElement;
          
          // Ensure all inputs have labels
          if (!htmlInput.getAttribute("aria-label") && !htmlInput.getAttribute("aria-labelledby")) {
            const label = form.querySelector(`label[for="${htmlInput.id}"]`);
            if (!label && htmlInput.placeholder) {
              htmlInput.setAttribute("aria-label", htmlInput.placeholder);
            }
          }
          
          // Add required field indicators
          if (htmlInput.required && !htmlInput.getAttribute("aria-required")) {
            htmlInput.setAttribute("aria-required", "true");
          }
          
          // Add error handling
          if (htmlInput.getAttribute("aria-invalid") === "true") {
            htmlInput.style.borderColor = "#ef4444";
            htmlInput.style.borderWidth = "2px";
          }
        });
      });
    };

    // Enhance navigation accessibility
    const enhanceNavigationAccessibility = () => {
      const navElements = document.querySelectorAll("nav");
      navElements.forEach((nav) => {
        if (!nav.getAttribute("aria-label") && !nav.getAttribute("aria-labelledby")) {
          nav.setAttribute("aria-label", "Main navigation");
        }
        
        // Add proper roles to navigation lists
        const lists = nav.querySelectorAll("ul");
        lists.forEach((list) => {
          if (!list.getAttribute("role")) {
            list.setAttribute("role", "menubar");
          }
          
          const listItems = list.querySelectorAll("li");
          listItems.forEach((item) => {
            if (!item.getAttribute("role")) {
              item.setAttribute("role", "menuitem");
            }
          });
        });
      });
    };

    // Add focus indicators
    const enhanceFocusIndicators = () => {
      const style = document.createElement("style");
      style.textContent = `
        /* Enhanced focus indicators for accessibility */
        *:focus {
          outline: 2px solid #8B4513 !important;
          outline-offset: 2px !important;
        }
        
        button:focus,
        a:focus,
        input:focus,
        select:focus,
        textarea:focus {
          box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.3) !important;
        }
        
        /* High contrast mode support */
        @media (prefers-contrast: high) {
          * {
            border-color: currentColor !important;
          }
        }
        
        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
          *,
          *::before,
          *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }
        
        /* Screen reader only class */
        .sr-only {
          position: absolute !important;
          width: 1px !important;
          height: 1px !important;
          padding: 0 !important;
          margin: -1px !important;
          overflow: hidden !important;
          clip: rect(0, 0, 0, 0) !important;
          white-space: nowrap !important;
          border: 0 !important;
        }
        
        .sr-only:focus {
          position: static !important;
          width: auto !important;
          height: auto !important;
          padding: inherit !important;
          margin: inherit !important;
          overflow: visible !important;
          clip: auto !important;
          white-space: inherit !important;
        }
        
        /* Mobile-first responsive design helpers */
        @media (max-width: 320px) {
          .container {
            padding-left: 1rem !important;
            padding-right: 1rem !important;
          }
        }
        
        @media (max-width: 375px) {
          button, .btn {
            min-height: 44px !important;
            min-width: 44px !important;
          }
        }
        
        @media (max-width: 768px) {
          /* Ensure touch targets are large enough */
          a, button, input, select, textarea, [role="button"] {
            min-height: 44px !important;
            min-width: 44px !important;
          }
          
          /* Improve text readability on mobile */
          body {
            font-size: 16px !important;
            line-height: 1.5 !important;
          }
        }
      `;
      document.head.appendChild(style);
    };

    // Add landmark roles
    const addLandmarkRoles = () => {
      // Add main landmark if not present
      const main = document.querySelector("main");
      if (main && !main.getAttribute("role")) {
        main.setAttribute("role", "main");
        main.id = main.id || "main-content";
      }
      
      // Add banner role to header
      const header = document.querySelector("header");
      if (header && !header.getAttribute("role")) {
        header.setAttribute("role", "banner");
      }
      
      // Add contentinfo role to footer
      const footer = document.querySelector("footer");
      if (footer && !footer.getAttribute("role")) {
        footer.setAttribute("role", "contentinfo");
      }
      
      // Add navigation role to nav elements
      const navs = document.querySelectorAll("nav");
      navs.forEach((nav) => {
        if (!nav.getAttribute("role")) {
          nav.setAttribute("role", "navigation");
        }
      });
    };

    // Initialize all accessibility enhancements
    enhanceKeyboardNavigation();
    enhanceTouchTargets();
    enhanceImageAccessibility();
    enhanceFormAccessibility();
    enhanceNavigationAccessibility();
    enhanceFocusIndicators();
    addLandmarkRoles();

    // Re-run enhancements when DOM changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
          setTimeout(() => {
            enhanceTouchTargets();
            enhanceImageAccessibility();
            enhanceFormAccessibility();
          }, 100);
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Cleanup function
    return () => {
      observer.disconnect();
    };
  }, []);

  return null; // This component doesn't render anything visible
}

// Helper function to check if element meets accessibility standards
export function checkAccessibility(element: HTMLElement) {
  const issues = [];
  
  // Check for alt text on images
  if (element.tagName === "IMG" && !element.getAttribute("alt")) {
    issues.push("Image missing alt text");
  }
  
  // Check for proper heading hierarchy
  if (element.tagName.match(/^H[1-6]$/)) {
    const level = parseInt(element.tagName.charAt(1));
    const prevHeading = element.previousElementSibling?.tagName.match(/^H[1-6]$/);
    if (prevHeading) {
      const prevLevel = parseInt(prevHeading[0].charAt(1));
      if (level > prevLevel + 1) {
        issues.push("Heading hierarchy skipped");
      }
    }
  }
  
  // Check for minimum touch target size
  const rect = element.getBoundingClientRect();
  if ((element.tagName === "BUTTON" || element.tagName === "A") && (rect.width < 44 || rect.height < 44)) {
    issues.push("Touch target too small (minimum 44px)");
  }
  
  // Check for proper labels on form elements
  if (["INPUT", "SELECT", "TEXTAREA"].includes(element.tagName)) {
    const hasLabel = element.getAttribute("aria-label") || 
                    element.getAttribute("aria-labelledby") ||
                    document.querySelector(`label[for="${element.id}"]`);
    if (!hasLabel) {
      issues.push("Form element missing label");
    }
  }
  
  return issues;
}

// Helper function to generate ARIA labels for common elements
export function generateAriaLabel(element: HTMLElement, context?: string) {
  const tagName = element.tagName.toLowerCase();
  const className = element.className;
  const textContent = element.textContent?.trim();
  
  if (tagName === "img") {
    if (className.includes("logo")) return "Chinioti Wooden Art logo";
    if (className.includes("product")) return "Handcrafted wooden furniture product image";
    if (className.includes("hero")) return "Traditional Chiniot furniture craftsman at work";
    return "Furniture image from Chiniot, Pakistan";
  }
  
  if (tagName === "button") {
    if (textContent) return textContent;
    if (className.includes("menu")) return "Open menu";
    if (className.includes("close")) return "Close";
    if (className.includes("cart")) return "Add to cart";
    return "Button";
  }
  
  if (tagName === "a") {
    if (textContent) return `Navigate to ${textContent}`;
    if (className.includes("logo")) return "Go to homepage";
    return "Link";
  }
  
  return context || textContent || "Interactive element";
}
