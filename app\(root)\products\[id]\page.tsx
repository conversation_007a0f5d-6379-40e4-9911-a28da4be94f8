"use client";
import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { getProductById, getRelatedProducts } from "@/apis/products";
import { Heart, ShoppingCart, Share2, ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import { useCart } from "@/contexts/CartContext";
import { useWishlist } from "@/contexts/WishlistContext";
import { useCurrency } from "@/contexts/CurrencyContext";
import ProductCard from "@/components/ProductCard";
import Link from "next/link";
import { IMAGE_PLACEHOLDER_URL } from "@/constants/helpers";
import ProductJsonLd from "@/components/SEO/ProductJsonLd";
import BreadcrumbJsonLd from "@/components/SEO/BreadcrumbJsonLd";
import ImageGallery from "@/components/ImageGallery";
import { generateMetadata } from "./generateMetadata";

const SingleProduct = () => {
  const { id } = useParams();
  const router = useRouter();
  const [product, setProduct] = useState<ProductData | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<ProductData[]>([]);
  const [quantity, setQuantity] = useState(1);
  const { addToCart } = useCart();
  const { isInWishlist, addToWishlist, removeFromWishlist } = useWishlist();
  const { formatPrice, convertPrice } = useCurrency();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchProductData = async () => {
      if (id) {
        setIsLoading(true);
        try {
          // Fetch product data
          const productData = await getProductById(id as string);
          if (productData) {
            setProduct(productData);

            // Fetch related products
            const related = await getRelatedProducts(id as string, 10);
            setRelatedProducts(related);
          }
        } catch (error) {
          console.error("Error fetching product:", error);
          // Handle error state if needed
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchProductData();
  }, [id]);

  const handleAddToCart = () => {
    if (product) {
      addToCart(product, quantity);
    }
  };

  const handleQuantityChange = (value: number) => {
    if (value >= 1) {
      setQuantity(value);
    }
  };

  // Calculate discounted price if available
  const discountedPrice = product?.discount
    ? Math.round(
        product.price - product.price * (parseInt(product.discount) / 100)
      )
    : null;

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-16 flex justify-center items-center">
        <div className="animate-pulse">Loading product details...</div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-2xl font-bold mb-4">Product Not Found</h1>
        <p className="mb-8">
          The product you&apos;re looking for doesn&apos;t exist or has been
          removed.
        </p>
        <Link href="/products">
          <Button variant="default">Browse Products</Button>
        </Link>
      </div>
    );
  }

  // Base URL for the site
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://chiniotiart.com";

  // Breadcrumb items for structured data
  const breadcrumbItems = [
    { name: "Home", url: "/" },
    { name: "Products", url: "/products" },
    { name: product.title || product.name, url: `/products/${product.id}` },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* SEO Structured Data */}
      <ProductJsonLd product={product} url={baseUrl} />
      <BreadcrumbJsonLd items={breadcrumbItems} baseUrl={baseUrl} />

      {/* Back button */}
      <div className="mb-6 relative z-10">
        <Button
          variant="ghost"
          className="flex items-center gap-2 text-gray-600 hover:text-white  "
          onClick={() => router.back()}
        >
          <ArrowLeft size={16} />
          <span>Back</span>
        </Button>
      </div>

      {/* Product details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 xl:gap-12 mb-16">
        {/* Product images */}
        <div className="w-full">
          <ImageGallery
            images={product.images}
            alt={product.title}
            className="w-full"
            showThumbnails={true}
            showZoom={true}
            priority={true}
          />
        </div>

        {/* Product info */}
        <div className="space-y-6">
          <div>
            <div className="text-sm text-gray-500  mb-1">
              {product.category}
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {product.title}
            </h1>

            {/* Price */}
            <div className="flex items-center gap-2 mb-4">
              {discountedPrice ? (
                <>
                  <span className="text-2xl font-bold text-accent">
                    {formatPrice(discountedPrice)}
                  </span>
                  <span className="text-lg text-gray-400 line-through">
                    {formatPrice(product.price)}
                  </span>
                  <span className="ml-2 px-2 py-1 bg-accent/10 text-accent text-sm font-medium rounded">
                    {product.discount}% OFF
                  </span>
                </>
              ) : (
                <span className="text-2xl font-bold text-gray-900">
                  {formatPrice(product.price)}
                </span>
              )}
            </div>

            {/* Availability */}
            <div className="mb-6">
              <span
                className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                  product.available
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {product.available ? "In Stock" : "Out of Stock"}
              </span>
            </div>

            {/* Description - placeholder since we don't have actual descriptions */}
            <div className=" max-w-none text-gray-600 mb-8">
              <p>
                Experience the exquisite craftsmanship of Chinioti wooden art
                with this beautiful {product.title.toLowerCase()}. Handcrafted
                by skilled artisans using traditional techniques passed down
                through generations.
              </p>
              <p>
                Made from premium quality wood, this piece combines durability
                with elegant design, making it a perfect addition to your home
                or office.
              </p>
            </div>

            {/* Quantity selector */}
            <div className="flex items-center gap-4 mb-6 relative z-10">
              <span className="text-gray-700">Quantity:</span>
              <div className="flex items-center border border-gray-300 rounded-md relative z-10">
                <button
                  type="button"
                  className="px-3 py-1 text-gray-600 hover:text-accent relative z-10"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleQuantityChange(quantity - 1);
                  }}
                  disabled={quantity <= 1}
                >
                  -
                </button>
                <span className="px-3 py-1 border-x border-gray-300 min-w-[40px] text-center">
                  {quantity}
                </span>
                <button
                  type="button"
                  className="px-3 py-1 text-gray-600 hover:text-accent relative z-10"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleQuantityChange(quantity + 1);
                  }}
                >
                  +
                </button>
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex flex-wrap gap-4 relative z-10">
              <Button
                className="flex items-center gap-2 text-white bg-amber-500 hover:text-white   px-6 py-2 "
                onClick={handleAddToCart}
                disabled={!product.available}
              >
                <ShoppingCart size={18} />
                <span>Add to Cart</span>
              </Button>

              <Button
                variant="outline"
                className={`flex items-center gap-2 relative z-10 ${
                  isInWishlist(product.id)
                    ? "bg-accent text-white border-accent hover:bg-accent/90 hover:text-white"
                    : "border-gray-300 text-gray-700 hover:bg-gray-50"
                }`}
                onClick={() => {
                  if (isInWishlist(product.id)) {
                    removeFromWishlist(product.id);
                  } else {
                    addToWishlist(product.id);
                  }
                }}
              >
                <Heart
                  size={18}
                  className={isInWishlist(product.id) ? "fill-white" : ""}
                />
                <span>
                  {isInWishlist(product.id)
                    ? "Saved to Wishlist"
                    : "Add to Wishlist"}
                </span>
              </Button>

              <Button
                variant="ghost"
                className="flex items-center gap-2 text-gray-700 hover:bg-gray-50 relative z-10"
              >
                <Share2 size={18} />
                <span>Share</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Related products */}
      {relatedProducts.length > 0 && (
        <div className="mt-16">
          <h2 className="text-2xl font-bold mb-6">Related Products</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {relatedProducts.map((product, idx) => (
              <ProductCard key={product.id} product={product} index={idx} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SingleProduct;
