"use client";
import React, { useState } from "react";
import ToolBar from "../components/ToolBar";
import { secondaryFont } from "@/constants/fonts";
import { motion } from "framer-motion";
import Link from "next/link";
import { usePathname } from "next/navigation";
import HamburgerMenu from "../components/ui/HamburgerMenu";
import MobileSidebar from "../components/navigation/MobileSidebar";
import { useAuth } from "@/contexts/AuthContext";
import {
  Home,
  Info,
  Phone,
  FileText,
  ShoppingBag,
  Heart,
  User,
} from "lucide-react";

const Header = () => {
  const pathname = usePathname();
  const { isAuthenticated } = useAuth();
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  const navItems = [
    { name: "About Us", path: "/about-us" },
    { name: "Contact Us", path: "/contact" },
    // { name: "VLOGS", path: "/video-blogs" },
    { name: "Blogs", path: "/blogs" },
  ];

  // Base mobile navigation items
  const baseMobileNavItems = [
    { name: "Home", path: "/", icon: <Home size={20} /> },
    { name: "About Us", path: "/about-us", icon: <Info size={20} /> },
    { name: "Contact Us", path: "/contact", icon: <Phone size={20} /> },
    { name: "Blogs", path: "/blogs", icon: <FileText size={20} /> },
    { name: "Products", path: "/products", icon: <ShoppingBag size={20} /> },
    { name: "Wishlist", path: "/wishlist", icon: <Heart size={20} /> },
  ];

  // Add profile tab only if user is authenticated
  const mobileNavItems = isAuthenticated
    ? [
        ...baseMobileNavItems,
        { name: "Profile", path: "/profile", icon: <User size={20} /> },
      ]
    : baseMobileNavItems;

  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  const closeMobileSidebar = () => {
    setIsMobileSidebarOpen(false);
  };

  return (
    <>
      <motion.header
        className="bg-white border-b border-gray-200 shadow-sm sticky top-0 z-50"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Main header */}
        <div className="responsive-header container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between min-h-[56px] sm:min-h-[64px] md:min-h-[72px] py-2 sm:py-3">
            {/* Mobile hamburger menu */}
            <div className="md:hidden flex-shrink-0">
              <HamburgerMenu
                isOpen={isMobileSidebarOpen}
                onClick={toggleMobileSidebar}
                size="md"
                className="text-gray-700 hover:text-accent"
                ariaLabel="Toggle navigation menu"
              />
            </div>

            {/* Logo */}
            <Link href="/" className="flex-shrink-0 mx-auto md:mx-0">
              <motion.div
                className={`${secondaryFont} responsive-logo font-bold relative`}
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <span className="text-accent">Chinioti</span>{" "}
                <span className="text-gray-800 hidden md:inline">
                  Wooden Art
                </span>
                <span className="text-gray-800 inline md:hidden">WA</span>
                <motion.div
                  className="absolute -bottom-1 left-0 h-0.5 bg-accent/30"
                  initial={{ width: "0%" }}
                  whileHover={{ width: "100%" }}
                  transition={{ duration: 0.3 }}
                />
              </motion.div>
            </Link>

            {/* Navigation - Desktop */}
            <nav className="hidden md:flex items-center space-x-6 lg:space-x-8">
              {navItems.map((item) => (
                <Link
                  key={item.path}
                  href={item.path}
                  className={`relative text-sm font-medium transition-colors duration-200 whitespace-nowrap ${
                    pathname === item.path
                      ? "text-accent"
                      : "text-gray-700 hover:text-accent"
                  }`}
                >
                  {item.name}
                  {pathname === item.path && (
                    <motion.div
                      className="absolute -bottom-1 left-0 right-0 h-0.5 bg-accent"
                      layoutId="activeTab"
                    />
                  )}
                </Link>
              ))}
            </nav>

            {/* Right side - Shop Now & Toolbar */}
            <div className="flex items-center space-x-1 sm:space-x-2 md:space-x-3 flex-shrink-0">
              <Link
                href="/products"
                className="hidden lg:inline-flex items-center px-3 py-1.5 border-white bg-accent text-white text-xs font-medium rounded-md hover:bg-accent/90 transition-colors duration-200 whitespace-nowrap"
              >
                Shop Now
              </Link>
              <ToolBar />
            </div>
          </div>
        </div>
      </motion.header>

      {/* Mobile Sidebar Navigation */}
      <MobileSidebar
        isOpen={isMobileSidebarOpen}
        onClose={closeMobileSidebar}
        navItems={mobileNavItems}
      />
    </>
  );
};

export default Header;
