"use client";
import React from "react";
import { motion } from "framer-motion";
import YouTubeBlogGrid from "@/components/YouTubeBlogGrid";
import AddYouTubeVideoHelper from "@/components/AddYouTubeVideoHelper";

const Blogs = () => {

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              <span className="text-accent">Vlogs</span>
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-8">
              Watch our collection of YouTube videos showcasing Chinioti wooden art, traditional craftsmanship techniques, and behind-the-scenes content.
            </p>
          </motion.div>
        </div>
      </section>

      {/* YouTube Blog Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <YouTubeBlogGrid
            itemsPerPage={12}
            showSearch={true}
            showFilters={true}
            showPagination={true}
            className="max-w-7xl mx-auto"
          />
        </div>
      </section>

      {/* Add Video Helper */}
      <AddYouTubeVideoHelper />
    </div>
  );
};

export default Blogs;
