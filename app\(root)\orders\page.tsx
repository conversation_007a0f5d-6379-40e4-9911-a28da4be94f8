"use client";
import React from "react";
import { useOrders } from "@/contexts/OrderContext";
import { useCurrency } from "@/contexts/CurrencyContext";
import { motion } from "framer-motion";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Package, ShoppingBag, Loader2 } from "lucide-react";
import ProtectedRoute from "@/components/auth/ProtectedRoute";

const OrdersPage = () => {
  const { orders, isLoading } = useOrders();
  const { formatPrice } = useCurrency();

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  // Function to get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "processing":
        return "bg-blue-100 text-blue-800";
      case "shipped":
        return "bg-purple-100 text-purple-800";
      case "delivered":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="container mx-auto px-4 py-16 text-center">
          <Loader2
            size={48}
            className="animate-spin mx-auto mb-4 text-accent"
          />
          <h2 className="text-xl font-semibold mb-2">Loading your orders...</h2>
          <p className="text-gray-500">
            Please wait while we fetch your order history.
          </p>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-8">My Orders</h1>

        {orders.length === 0 ? (
          <motion.div
            className="flex flex-col items-center justify-center py-16 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <Package size={64} className="text-gray-300 mb-4" />
            <h2 className="text-xl font-semibold mb-2">No orders yet</h2>
            <p className="text-gray-500 mb-6">
              You haven't placed any orders yet.
            </p>
            <Link href="/products">
              <Button variant="default">Start Shopping</Button>
            </Link>
          </motion.div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="p-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold">Order History</h2>
            </div>

            <div className="divide-y divide-gray-100">
              {orders.map((order) => (
                <motion.div
                  key={order.id}
                  className="p-4 hover:bg-gray-50 transition-colors"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                    <div>
                      <div className="flex items-center gap-2">
                        <ShoppingBag size={16} className="text-gray-400" />
                        <Link href={`/orders/${order.id}`}>
                          <span className="font-medium text-accent hover:underline">
                            {order.id}
                          </span>
                        </Link>
                      </div>
                      <div className="text-sm text-gray-500 mt-1">
                        Placed on {formatDate(order.createdAt)}
                      </div>
                    </div>

                    <div className="flex flex-col md:flex-row items-start md:items-center gap-3">
                      <div className="text-right">
                        <div className="font-medium">
                          {formatPrice(order.total)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {order.items.length} items
                        </div>
                      </div>

                      <div
                        className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(
                          order.status
                        )}`}
                      >
                        {order.status.charAt(0).toUpperCase() +
                          order.status.slice(1)}
                      </div>

                      <Link href={`/orders/${order.id}`}>
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                      </Link>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
};

export default OrdersPage;
