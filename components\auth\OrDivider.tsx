"use client";
import React from 'react';
import { motion } from 'framer-motion';

interface OrDividerProps {
  text?: string;
  className?: string;
}

const OrDivider: React.FC<OrDividerProps> = ({
  text = 'OR',
  className = '',
}) => {
  return (
    <div className={`relative my-6 ${className}`}>
      <div className="absolute inset-0 flex items-center">
        <div className="w-full border-t border-gray-300" />
      </div>
      <div className="relative flex justify-center text-sm">
        <motion.span 
          className="px-4 bg-card text-gray-500 uppercase"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          {text}
        </motion.span>
      </div>
    </div>
  );
};

export default OrDivider;
