"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Lock, Save, X } from "lucide-react";
import { changePassword } from "@/apis/user";

interface PasswordChangeFormProps {
  userId: string;
  onCancel: () => void;
}

const PasswordChangeForm: React.FC<PasswordChangeFormProps> = ({
  userId,
  onCancel,
}) => {
  const [formData, setFormData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user types
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    let valid = true;
    const newErrors = {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    };

    if (!formData.currentPassword) {
      newErrors.currentPassword = "Current password is required";
      valid = false;
    }

    if (!formData.newPassword) {
      newErrors.newPassword = "New password is required";
      valid = false;
    } else if (formData.newPassword.length < 8) {
      newErrors.newPassword = "Password must be at least 8 characters long";
      valid = false;
    } else if (
      !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).+$/.test(
        formData.newPassword
      )
    ) {
      newErrors.newPassword =
        "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character";
      valid = false;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
      valid = false;
    }

    setErrors(newErrors);
    return valid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      console.log("Submitting password change for user:", userId);

      // Validate userId is present
      if (!userId) {
        toast.error("User ID is missing. Please try logging in again.");
        console.error("User ID is missing when attempting to change password");
        return;
      }

      // Get token from localStorage to verify it's present
      const token = localStorage.getItem("token");
      if (!token) {
        toast.error(
          "Authentication token is missing. Please try logging in again."
        );
        console.error(
          "Auth token is missing when attempting to change password"
        );
        return;
      }

      const success = await changePassword(
        userId,
        formData.currentPassword,
        formData.newPassword
      );

      if (success) {
        toast.success("Password changed successfully");
        // Reset form
        setFormData({
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
        });
        onCancel();
      } else {
        toast.error("Failed to change password");
      }
    } catch (error: any) {
      console.error("Error changing password in component:", error);

      // More detailed error handling
      if (error.response) {
        console.error("Error response status:", error.response.status);
        console.error("Error response data:", error.response.data);

        if (error.response.status === 401) {
          setErrors((prev) => ({
            ...prev,
            currentPassword: "Current password is incorrect",
          }));
          toast.error("Current password is incorrect");
        } else if (error.response.status === 404) {
          toast.error(
            "Password change option not found. Please contact support."
          );
        } else if (error.response.status === 400) {
          const errorMessage =
            error.response?.data?.message || "Invalid input data";
          toast.error(errorMessage);

          // Check for specific validation errors
          if (errorMessage.toLowerCase().includes("password")) {
            setErrors((prev) => ({
              ...prev,
              newPassword: errorMessage,
            }));
          }
        } else {
          toast.error(
            error.response?.data?.message || "Failed to change password"
          );
        }
      } else if (error.request) {
        // Request was made but no response received
        console.error("No response received:", error.request);
        toast.error("Network error. Please check your internet connection.");
      } else {
        // Something else happened
        console.error("Error message:", error.message);
        toast.error("An unexpected error occurred. Please try again later.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="space-y-4 mt-6 border-t border-accent pt-6"
    >
      <h3 className="text-lg font-semibold mb-4">Change Password</h3>

      {/* Current Password */}
      <div className="space-y-2">
        <Label htmlFor="currentPassword" className="flex items-center gap-2">
          <Lock size={16} />
          <span>Current Password</span>
        </Label>
        <Input
          id="currentPassword"
          name="currentPassword"
          type="password"
          value={formData.currentPassword}
          onChange={handleInputChange}
          disabled={isLoading}
          required
        />
        {errors.currentPassword && (
          <p className="text-red-500 text-xs mt-1">{errors.currentPassword}</p>
        )}
      </div>

      {/* New Password */}
      <div className="space-y-2">
        <Label htmlFor="newPassword" className="flex items-center gap-2">
          <Lock size={16} />
          <span>New Password</span>
        </Label>
        <Input
          id="newPassword"
          name="newPassword"
          type="password"
          value={formData.newPassword}
          onChange={handleInputChange}
          disabled={isLoading}
          required
        />
        {errors.newPassword && (
          <p className="text-red-500 text-xs mt-1">{errors.newPassword}</p>
        )}
      </div>

      {/* Confirm Password */}
      <div className="space-y-2">
        <Label htmlFor="confirmPassword" className="flex items-center gap-2">
          <Lock size={16} />
          <span>Confirm New Password</span>
        </Label>
        <Input
          id="confirmPassword"
          name="confirmPassword"
          type="password"
          value={formData.confirmPassword}
          onChange={handleInputChange}
          disabled={isLoading}
          required
        />
        {errors.confirmPassword && (
          <p className="text-red-500 text-xs mt-1">{errors.confirmPassword}</p>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-4 mt-6">
        <Button
          type="submit"
          disabled={isLoading}
          className="flex items-center gap-2"
        >
          <Save size={16} />
          <span>Change Password</span>
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
          className="flex items-center gap-2"
        >
          <X size={16} />
          <span>Cancel</span>
        </Button>
      </div>
    </form>
  );
};

export default PasswordChangeForm;
